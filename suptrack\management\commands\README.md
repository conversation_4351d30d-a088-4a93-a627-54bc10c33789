# SmartSupply Management Commands

This directory contains comprehensive Django management commands for the SmartSupply application, providing powerful tools for user account management and inventory operations.

## Commands Overview

### 1. User Account Management (`manage_users.py`)
Comprehensive user account management with role assignments, bulk operations, and reporting.

### 2. Inventory Management (`manage_inventory.py`)
Complete inventory management including bulk import/export, stock updates, QR code generation, and reporting.

## Installation Requirements

```bash
# Required for Excel file support
pip install pandas openpyxl

# Required for QR code generation
pip install qrcode[pil]
```

## User Management Command

### Basic Usage
```bash
python manage.py manage_users <action> [options]
```

### Available Actions

#### Create User
```bash
# Create a new department user
python manage.py manage_users create \
    --username john_doe \
    --email <EMAIL> \
    --first_name John \
    --last_name Doe \
    --role department_user \
    --department IT \
    --phone "555-0101" \
    --send_email

# Create GSO staff member
python manage.py manage_users create \
    --username jane_admin \
    --email <EMAIL> \
    --role gso_staff \
    --department GSO \
    --send_email
```

#### Update User
```bash
# Update user role and department
python manage.py manage_users update \
    --username john_doe \
    --role gso_staff \
    --department GSO

# Deactivate user
python manage.py manage_users update \
    --username john_doe \
    --active False
```

#### Bulk Import Users
```bash
# Import users from CSV file
python manage.py manage_users bulk_import \
    --file users.csv \
    --send_emails

# Validate CSV without importing
python manage.py manage_users bulk_import \
    --file users.csv \
    --dry_run
```

#### Reset Password
```bash
# Reset password and send email
python manage.py manage_users reset_password \
    --username john_doe \
    --send_email

# Reset with specific password
python manage.py manage_users reset_password \
    --username john_doe \
    --password "NewSecurePassword123!" \
    --send_email
```

#### Generate Reports
```bash
# Generate user activity report
python manage.py manage_users report \
    --output user_report.csv \
    --days 30 \
    --format csv

# Filter by role and department
python manage.py manage_users report \
    --role gso_staff \
    --department GSO \
    --output gso_report.json \
    --format json
```

#### List Users
```bash
# List all users in table format
python manage.py manage_users list

# Filter and export to CSV
python manage.py manage_users list \
    --role department_user \
    --active True \
    --format csv > active_users.csv
```

#### Delete User
```bash
# Delete user account (requires confirmation)
python manage.py manage_users delete \
    --username john_doe \
    --confirm
```

### CSV Format for Bulk Import

Required columns:
- `username` - Unique username
- `email` - Email address
- `role` - One of: admin, gso_staff, department_user
- `department` - Department name

Optional columns:
- `first_name` - First name
- `last_name` - Last name
- `phone` - Phone number
- `password` - Password (auto-generated if not provided)

Example CSV:
```csv
username,email,first_name,last_name,role,department,phone
john_doe,<EMAIL>,John,Doe,department_user,IT,555-0101
jane_admin,<EMAIL>,Jane,Admin,gso_staff,GSO,555-0102
```

## Inventory Management Command

### Basic Usage
```bash
python manage.py manage_inventory <action> [options]
```

### Available Actions

#### Import Inventory
```bash
# Import from CSV file
python manage.py manage_inventory import \
    --file inventory.csv \
    --create_qr

# Import from Excel file
python manage.py manage_inventory import \
    --file inventory.xlsx \
    --format excel \
    --update_existing

# Validate without importing
python manage.py manage_inventory import \
    --file inventory.csv \
    --dry_run
```

#### Export Inventory
```bash
# Export all items to CSV
python manage.py manage_inventory export \
    --file current_inventory.csv \
    --format csv

# Export only low stock items
python manage.py manage_inventory export \
    --file low_stock.csv \
    --low_stock_only

# Export specific category to Excel
python manage.py manage_inventory export \
    --file electronics.xlsx \
    --format excel \
    --category Electronics
```

#### Update Stock Levels
```bash
# Set stock level
python manage.py manage_inventory update_stock \
    --item_code IT001 \
    --quantity 50 \
    --reason "Stock replenishment" \
    --adjustment_type set

# Add to existing stock
python manage.py manage_inventory update_stock \
    --item_code IT001 \
    --quantity 10 \
    --reason "Additional purchase" \
    --adjustment_type add

# Subtract from stock
python manage.py manage_inventory update_stock \
    --item_code IT001 \
    --quantity 5 \
    --reason "Damaged items removed" \
    --adjustment_type subtract
```

#### Generate Low Stock Report
```bash
# Generate low stock report
python manage.py manage_inventory low_stock \
    --output low_stock_report.csv

# Custom threshold
python manage.py manage_inventory low_stock \
    --threshold 5 \
    --format json \
    --output critical_stock.json
```

#### Inventory Reconciliation
```bash
# Reconcile against physical count
python manage.py manage_inventory reconcile \
    --file physical_count.csv \
    --create_adjustments \
    --tolerance 2
```

#### Generate QR Codes
```bash
# Generate QR codes for all items
python manage.py manage_inventory generate_qr \
    --output_dir qr_codes/ \
    --size 10

# Generate for specific items
python manage.py manage_inventory generate_qr \
    --output_dir qr_codes/ \
    --item_codes IT001 IT002 OF001
```

#### Usage Reports
```bash
# Generate 30-day usage report
python manage.py manage_inventory usage_report \
    --days 30 \
    --output usage_report.csv \
    --top_items 25

# JSON format with 90-day analysis
python manage.py manage_inventory usage_report \
    --days 90 \
    --format json \
    --output quarterly_usage.json
```

#### Update Pricing
```bash
# Update pricing from CSV
python manage.py manage_inventory update_pricing \
    --file pricing_updates.csv

# Validate pricing without updating
python manage.py manage_inventory update_pricing \
    --file pricing_updates.csv \
    --dry_run
```

#### Comprehensive Reports
```bash
# Generate full inventory report
python manage.py manage_inventory report \
    --output inventory_report.csv \
    --include_costs

# JSON format with cost analysis
python manage.py manage_inventory report \
    --format json \
    --include_costs \
    --output detailed_inventory.json
```

#### List Items
```bash
# List all items in table format
python manage.py manage_inventory list

# Filter and search
python manage.py manage_inventory list \
    --category Electronics \
    --low_stock \
    --search "laptop"

# Export filtered results
python manage.py manage_inventory list \
    --category Stationery \
    --format csv > stationery_items.csv
```

### CSV Formats

#### Inventory Import Format
Required columns:
- `item_code` - Unique item identifier
- `name` - Item name
- `category` - Item category
- `unit_of_measure` - Unit (Each, Pack, Box, etc.)
- `unit_price` - Price per unit

Optional columns:
- `description` - Item description
- `current_stock` - Current stock level (default: 0)
- `reorder_level` - Reorder threshold (default: 10)
- `supplier` - Supplier name
- `location` - Storage location

#### Physical Count Format (for reconciliation)
Required columns:
- `item_code` - Item identifier
- `physical_count` - Actual counted quantity

#### Pricing Update Format
Required columns:
- `item_code` - Item identifier
- `new_price` - New unit price

Optional columns:
- `effective_date` - When price takes effect

## Error Handling and Logging

Both commands include comprehensive error handling:
- Input validation with detailed error messages
- Transaction rollback on failures
- Detailed logging of all operations
- Progress feedback for long-running operations

## Best Practices

1. **Always use dry-run first** for bulk operations
2. **Backup your database** before major changes
3. **Test with small datasets** before full imports
4. **Review validation errors** carefully before proceeding
5. **Use transaction safety** - operations are atomic
6. **Monitor logs** for audit trails

## Sample Files

- `sample_users.csv` - Example user import format
- `sample_inventory.csv` - Example inventory import format

## Support

For issues or questions about these management commands, please refer to the SmartSupply documentation or contact the development team.
