import os
import sys
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smartsupply.settings')
django.setup()

from django.contrib.auth.models import User
from django.utils import timezone
from datetime import <PERSON>elta
import uuid
from suptrack.models import QRCode, ScanActivity, SupplyItem, Category, RequestItem, SupplyRequest

def verify_scan_activity_model():
    print("\n=== Verifying ScanActivity Model ===")
    
    # Check if the models exist
    print("Checking model existence...")
    print(f"QRCode model exists: {QRCode.__name__}")
    print(f"ScanActivity model exists: {ScanActivity.__name__}")
    
    # Check model fields
    print("\nChecking QRCode fields...")
    qrcode_fields = [f.name for f in QRCode._meta.get_fields()]
    print(f"QRCode fields: {', '.join(qrcode_fields)}")
    
    print("\nChecking ScanActivity fields...")
    scan_activity_fields = [f.name for f in ScanActivity._meta.get_fields()]
    print(f"ScanActivity fields: {', '.join(scan_activity_fields)}")
    
    # Check existing data
    print("\nChecking existing data...")
    qrcodes = QRCode.objects.all()
    print(f"Number of QR codes: {qrcodes.count()}")
    
    scan_activities = ScanActivity.objects.all()
    print(f"Number of scan activities: {scan_activities.count()}")
    
    # Create test data if no QR codes exist
    if qrcodes.count() == 0:
        print("\nNo QR codes found. Creating test data...")
        try:
            # Get or create test user
            test_username = 'testuser_scan_activity'
            try:
                test_user = User.objects.get(username=test_username)
                print(f"Using existing user: {test_username}")
            except User.DoesNotExist:
                test_user = User.objects.create_user(
                    username=test_username,
                    email='<EMAIL>',
                    password='password123'
                )
                print(f"Created new user: {test_username}")
            
            # Get or create test category
            test_category_name = 'Test Category Scan Activity'
            try:
                test_category = Category.objects.get(name=test_category_name)
                print(f"Using existing category: {test_category_name}")
            except Category.DoesNotExist:
                test_category = Category.objects.create(
                    name=test_category_name,
                    description='Test category for scan activity test'
                )
                print(f"Created new category: {test_category_name}")
            
            # Get or create test supply item
            test_item_name = 'Test Item Scan Activity'
            try:
                test_supply_item = SupplyItem.objects.get(name=test_item_name)
                print(f"Using existing supply item: {test_item_name}")
            except SupplyItem.DoesNotExist:
                test_supply_item = SupplyItem.objects.create(
                    item_code=f'ITEM-{uuid.uuid4().hex[:8]}',
                    name=test_item_name,
                    description='Test item for scan activity test',
                    category=test_category,
                    unit_of_measure='Each',
                    unit_cost=10.00,
                    current_stock=100,
                    minimum_threshold=20,
                    supplier='Test Supplier'
                )
                print(f"Created new supply item: {test_item_name}")
            
            # Create a test supply request
            test_request_id = f'REQ-SCAN-{uuid.uuid4().hex[:8]}'
            test_request = SupplyRequest.objects.create(
                request_id=test_request_id,
                requester=test_user,
                department='IT',
                status='pending',
                justification='Test justification for scan activity test',
                required_date=timezone.now().date() + timedelta(days=7),
                notes='Test notes for scan activity test'
            )
            print(f"Created new supply request: {test_request_id}")
            
            # Create a test request item
            test_request_item = RequestItem.objects.create(
                request=test_request,
                supply_item=test_supply_item,
                quantity_requested=5,
                unit_price=test_supply_item.unit_cost,
                notes='Test item notes for scan activity test'
            )
            print(f"Created new request item for {test_request_id}")
            
            # Create a test QR code
            test_qr_code = QRCode.objects.create(
                supply_item=test_supply_item,
                request_item=test_request_item,
                is_active=True
            )
            print(f"Created new QR code: {test_qr_code.code_id}")
            
            # Create a test scan activity
            test_scan_activity = ScanActivity.objects.create(
                qr_code=test_qr_code,
                scanned_by=test_user,
                scan_type='general',
                success=True,
                ip_address='127.0.0.1',
                user_agent='Test User Agent'
            )
            print(f"Created new scan activity: {test_scan_activity.id}")
            
            # Refresh data counts
            qrcodes = QRCode.objects.all()
            scan_activities = ScanActivity.objects.all()
            print(f"\nUpdated counts:")
            print(f"Number of QR codes: {qrcodes.count()}")
            print(f"Number of scan activities: {scan_activities.count()}")
            
        except Exception as e:
            print(f"Error creating test data: {e}")
    
    # Display a sample QR code if available
    if qrcodes.exists():
        sample_qrcode = qrcodes.first()
        print(f"\nSample QRCode:")
        print(f"  ID: {sample_qrcode.id}")
        print(f"  Code ID: {sample_qrcode.code_id}")
        print(f"  Supply Item: {sample_qrcode.supply_item.name}")
        print(f"  Request Item: {sample_qrcode.request_item.id if sample_qrcode.request_item else 'None'}")
        print(f"  Generated Date: {sample_qrcode.generated_date}")
        print(f"  Is Active: {sample_qrcode.is_active}")
        print(f"  Scanned Count: {sample_qrcode.scanned_count}")
        
        # Display associated scan activities
        sample_activities = sample_qrcode.scan_activities.all()
        if sample_activities.exists():
            print(f"\n  Associated ScanActivities:")
            for activity in sample_activities:
                print(f"    - ID: {activity.id}, Type: {activity.scan_type}, Scanned At: {activity.scanned_at}, Success: {activity.success}")
    
    # Display a sample scan activity if available
    if scan_activities.exists():
        sample_activity = scan_activities.first()
        print(f"\nSample ScanActivity:")
        print(f"  ID: {sample_activity.id}")
        print(f"  QR Code: {sample_activity.qr_code.code_id if sample_activity.qr_code else 'None'}")
        print(f"  Scanned By: {sample_activity.scanned_by.username}")
        print(f"  Scan Type: {sample_activity.scan_type}")
        print(f"  Scanned At: {sample_activity.scanned_at}")
        print(f"  Success: {sample_activity.success}")
        print(f"  IP Address: {sample_activity.ip_address}")
    
    print("\nVerification complete!")

if __name__ == '__main__':
    verify_scan_activity_model()