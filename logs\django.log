INFO 2025-07-24 21:49:06,654 autoreload 10100 23044 Watching for file changes with StatReloader
INFO 2025-07-24 21:49:11,136 basehttp 10100 5200 "GET /admin/?ide_webview_request_time=1753364951068 HTTP/1.1" 302 0
INFO 2025-07-24 21:49:11,457 basehttp 10100 5200 "GET /admin/login/?next=/admin/%3Fide_webview_request_time%3D1753364951068 HTTP/1.1" 200 4255
INFO 2025-07-24 21:49:11,841 basehttp 10100 20892 "GET /static/admin/css/login.css HTTP/1.1" 200 951
INFO 2025-07-24 21:49:11,905 basehttp 10100 15500 "GET /static/admin/css/dark_mode.css HTTP/1.1" 200 2808
INFO 2025-07-24 21:49:11,916 basehttp 10100 5200 "GET /static/admin/css/base.css HTTP/1.1" 200 22120
INFO 2025-07-24 21:49:11,921 basehttp 10100 15608 "GET /static/admin/css/responsive.css HTTP/1.1" 200 16565
INFO 2025-07-24 21:49:11,929 basehttp 10100 24428 "GET /static/admin/css/nav_sidebar.css HTTP/1.1" 200 2810
INFO 2025-07-24 21:49:11,931 basehttp 10100 25320 "GET /static/admin/js/theme.js HTTP/1.1" 200 1653
INFO 2025-07-24 21:49:11,937 basehttp 10100 25320 "GET /static/admin/js/nav_sidebar.js HTTP/1.1" 200 3063
WARNING 2025-07-24 21:49:12,056 log 10100 25320 Not Found: /@vite/client
WARNING 2025-07-24 21:49:12,057 basehttp 10100 25320 "GET /@vite/client HTTP/1.1" 404 2555
INFO 2025-07-24 21:49:29,855 basehttp 10100 14272 "POST /admin/login/?next=/admin/%3Fide_webview_request_time%3D1753364951068 HTTP/1.1" 302 0
INFO 2025-07-24 21:49:29,866 middleware 10100 14272 New session started for user: admin from IP: 127.0.0.1
INFO 2025-07-24 21:49:29,903 basehttp 10100 14272 "GET /admin/?ide_webview_request_time=1753364951068 HTTP/1.1" 200 11693
INFO 2025-07-24 21:49:29,919 basehttp 10100 14272 "GET /static/admin/css/dashboard.css HTTP/1.1" 200 441
INFO 2025-07-24 21:49:29,950 basehttp 10100 14272 "GET /static/admin/img/icon-addlink.svg HTTP/1.1" 200 331
INFO 2025-07-24 21:49:29,951 basehttp 10100 16204 "GET /static/admin/img/icon-changelink.svg HTTP/1.1" 200 380
ERROR 2025-07-24 21:49:30,002 middleware 10100 16204 User without profile attempted access: admin
ERROR 2025-07-24 21:49:30,104 log 10100 16204 Internal Server Error: /@vite/client
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\suptrack\middleware.py", line 52, in process_request
    profile = request.user.userprofile
              ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\utils\functional.py", line 253, in inner
    return func(_wrapped, *args)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\models\fields\related_descriptors.py", line 534, in __get__
    raise self.RelatedObjectDoesNotExist(
    ...<2 lines>...
    )
django.contrib.auth.models.User.userprofile.RelatedObjectDoesNotExist: User has no userprofile.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\contrib\messages\api.py", line 27, in add_message
    messages = request._messages
               ^^^^^^^^^^^^^^^^^
AttributeError: 'WSGIRequest' object has no attribute '_messages'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\utils\deprecation.py", line 119, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\middleware.py", line 73, in process_request
    messages.error(
    ~~~~~~~~~~~~~~^
        request,
        ^^^^^^^^
        "User profile not found. Please contact an administrator."
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\contrib\messages\api.py", line 121, in error
    add_message(
    ~~~~~~~~~~~^
        request,
        ^^^^^^^^
    ...<3 lines>...
        fail_silently=fail_silently,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\contrib\messages\api.py", line 35, in add_message
    raise MessageFailure(
    ...<2 lines>...
    )
django.contrib.messages.api.MessageFailure: You cannot add messages without installing django.contrib.messages.middleware.MessageMiddleware
ERROR 2025-07-24 21:49:30,122 basehttp 10100 16204 "GET /@vite/client HTTP/1.1" 500 96470
INFO 2025-07-24 22:02:21,999 autoreload 13748 17360 Watching for file changes with StatReloader
INFO 2025-07-24 22:02:30,151 basehttp 13748 13944 - Broken pipe from ('127.0.0.1', 54711)
INFO 2025-07-24 22:02:30,167 basehttp 13748 14072 "GET /admin/?ide_webview_request_time=1753364951068 HTTP/1.1" 200 11043
ERROR 2025-07-24 22:02:30,448 middleware 13748 14072 User without profile attempted access: admin
ERROR 2025-07-24 22:02:30,481 log 13748 14072 Internal Server Error: /@vite/client
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\suptrack\middleware.py", line 52, in process_request
    profile = request.user.userprofile
              ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\utils\functional.py", line 253, in inner
    return func(_wrapped, *args)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\models\fields\related_descriptors.py", line 534, in __get__
    raise self.RelatedObjectDoesNotExist(
    ...<2 lines>...
    )
django.contrib.auth.models.User.userprofile.RelatedObjectDoesNotExist: User has no userprofile.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\contrib\messages\api.py", line 27, in add_message
    messages = request._messages
               ^^^^^^^^^^^^^^^^^
AttributeError: 'WSGIRequest' object has no attribute '_messages'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\utils\deprecation.py", line 119, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\middleware.py", line 73, in process_request
    messages.error(
    ~~~~~~~~~~~~~~^
        request,
        ^^^^^^^^
        "User profile not found. Please contact an administrator."
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\contrib\messages\api.py", line 121, in error
    add_message(
    ~~~~~~~~~~~^
        request,
        ^^^^^^^^
    ...<3 lines>...
        fail_silently=fail_silently,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\contrib\messages\api.py", line 35, in add_message
    raise MessageFailure(
    ...<2 lines>...
    )
django.contrib.messages.api.MessageFailure: You cannot add messages without installing django.contrib.messages.middleware.MessageMiddleware
ERROR 2025-07-24 22:02:30,489 basehttp 13748 14072 "GET /@vite/client HTTP/1.1" 500 96470
INFO 2025-07-24 22:11:28,061 autoreload 13748 17360 C:\Users\<USER>\dev\smartsupply\suptrack\middleware.py changed, reloading.
INFO 2025-07-24 22:11:29,278 autoreload 25056 9268 Watching for file changes with StatReloader
INFO 2025-07-24 22:11:40,012 autoreload 11948 22800 Watching for file changes with StatReloader
ERROR 2025-07-24 22:11:44,668 middleware 11948 2276 Exception for user admin: NoReverseMatch: Reverse for 'dashboard' not found. 'dashboard' is not a valid view function or pattern name.
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\views.py", line 9, in dashboard
    return render(request, 'dashboard.html', context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'dashboard' not found. 'dashboard' is not a valid view function or pattern name.
ERROR 2025-07-24 22:11:44,761 log 11948 2276 Internal Server Error: /
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\views.py", line 9, in dashboard
    return render(request, 'dashboard.html', context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'dashboard' not found. 'dashboard' is not a valid view function or pattern name.
ERROR 2025-07-24 22:11:44,767 basehttp 11948 2276 "GET /?ide_webview_request_time=1753366304628 HTTP/1.1" 500 143699
WARNING 2025-07-24 22:11:44,960 log 11948 2276 Not Found: /@vite/client
WARNING 2025-07-24 22:11:44,964 basehttp 11948 2276 "GET /@vite/client HTTP/1.1" 404 2806
INFO 2025-07-24 22:12:12,106 autoreload 15400 1952 Watching for file changes with StatReloader
INFO 2025-07-24 22:12:17,288 basehttp 15400 8800 "GET /?ide_webview_request_time=1753366337246 HTTP/1.1" 200 33013
INFO 2025-07-24 22:12:17,407 basehttp 15400 15756 "GET /static/js/dashboard.js HTTP/1.1" 200 4064
INFO 2025-07-24 22:12:17,410 basehttp 15400 8800 "GET /static/css/dashboard.css HTTP/1.1" 200 1251
WARNING 2025-07-24 22:12:20,236 log 15400 8800 Not Found: /@vite/client
WARNING 2025-07-24 22:12:20,237 basehttp 15400 8800 "GET /@vite/client HTTP/1.1" 404 2806
INFO 2025-07-24 22:15:06,249 basehttp 15400 24516 "GET /?ide_webview_request_time=1753366337246 HTTP/1.1" 200 33013
INFO 2025-07-24 22:15:06,282 basehttp 15400 24516 "GET /?ide_webview_request_time=1753366337246 HTTP/1.1" 200 33013
INFO 2025-07-24 22:15:06,355 basehttp 15400 22212 "GET /static/js/dashboard.js HTTP/1.1" 304 0
INFO 2025-07-24 22:15:06,355 basehttp 15400 24516 "GET /static/css/dashboard.css HTTP/1.1" 304 0
WARNING 2025-07-24 22:15:06,889 log 15400 24516 Not Found: /@vite/client
WARNING 2025-07-24 22:15:06,889 basehttp 15400 24516 "GET /@vite/client HTTP/1.1" 404 2806
INFO 2025-07-24 23:29:52,086 autoreload 15820 13316 Watching for file changes with StatReloader
INFO 2025-07-24 23:30:07,198 basehttp 15820 15492 "GET / HTTP/1.1" 200 33013
INFO 2025-07-24 23:30:08,217 basehttp 15820 15492 "GET /static/css/dashboard.css HTTP/1.1" 200 1251
INFO 2025-07-24 23:30:08,225 basehttp 15820 15252 "GET /static/js/dashboard.js HTTP/1.1" 200 4064
WARNING 2025-07-24 23:30:11,937 log 15820 11964 Not Found: /favicon.ico
WARNING 2025-07-24 23:30:11,968 basehttp 15820 11964 "GET /favicon.ico HTTP/1.1" 404 2803
INFO 2025-07-24 23:36:02,592 autoreload 15820 13316 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-24 23:36:03,771 autoreload 22920 2964 Watching for file changes with StatReloader
INFO 2025-07-24 23:36:15,346 autoreload 22920 2964 C:\Users\<USER>\dev\smartsupply\suptrack\urls.py changed, reloading.
INFO 2025-07-24 23:36:16,161 autoreload 22168 2636 Watching for file changes with StatReloader
INFO 2025-07-24 23:37:21,633 autoreload 22168 2636 C:\Users\<USER>\dev\smartsupply\suptrack\forms.py changed, reloading.
INFO 2025-07-24 23:37:22,557 autoreload 18624 24968 Watching for file changes with StatReloader
INFO 2025-07-24 23:39:54,672 autoreload 18624 24968 C:\Users\<USER>\dev\smartsupply\suptrack\forms.py changed, reloading.
INFO 2025-07-24 23:39:55,469 autoreload 24920 25288 Watching for file changes with StatReloader
INFO 2025-07-24 23:40:22,246 autoreload 24920 25288 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-24 23:40:23,063 autoreload 15140 4764 Watching for file changes with StatReloader
INFO 2025-07-24 23:41:32,708 autoreload 15140 4764 C:\Users\<USER>\dev\smartsupply\suptrack\models.py changed, reloading.
INFO 2025-07-24 23:41:33,874 autoreload 23768 13648 Watching for file changes with StatReloader
INFO 2025-07-24 23:41:59,646 autoreload 23516 25932 Watching for file changes with StatReloader
INFO 2025-07-24 23:47:16,078 basehttp 23768 7152 "GET / HTTP/1.1" 302 0
INFO 2025-07-24 23:47:16,145 basehttp 23768 7152 "GET /login/?next=/ HTTP/1.1" 200 16568
INFO 2025-07-24 23:47:16,548 basehttp 23768 7152 "GET /static/css/dashboard.css HTTP/1.1" 200 1251
INFO 2025-07-24 23:47:16,555 basehttp 23768 6208 "GET /static/js/dashboard.js HTTP/1.1" 200 4064
WARNING 2025-07-24 23:47:16,690 log 23768 6208 Not Found: /favicon.ico
WARNING 2025-07-24 23:47:16,693 basehttp 23768 6208 "GET /favicon.ico HTTP/1.1" 404 4421
INFO 2025-07-24 23:47:20,707 basehttp 23768 6208 "GET / HTTP/1.1" 302 0
INFO 2025-07-24 23:47:20,718 basehttp 23768 6208 "GET /login/?next=/ HTTP/1.1" 200 16568
INFO 2025-07-24 23:47:26,345 basehttp 23768 6208 "GET /register/ HTTP/1.1" 200 17903
INFO 2025-07-24 23:48:00,300 basehttp 23768 6208 "POST /register/ HTTP/1.1" 200 18103
INFO 2025-07-24 23:48:11,953 basehttp 23768 6208 "GET /login/ HTTP/1.1" 200 16568
INFO 2025-07-24 23:48:20,632 basehttp 23768 6208 "POST /login/ HTTP/1.1" 302 0
INFO 2025-07-24 23:48:20,645 middleware 23768 6208 New session started for user: lanzy from IP: 127.0.0.1
INFO 2025-07-24 23:48:20,661 basehttp 23768 6208 "GET / HTTP/1.1" 200 33013
INFO 2025-07-24 23:52:01,369 autoreload 23768 13648 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-24 23:52:01,676 autoreload 23516 25932 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-24 23:52:02,296 autoreload 16284 4196 Watching for file changes with StatReloader
INFO 2025-07-24 23:52:02,631 autoreload 11396 8788 Watching for file changes with StatReloader
INFO 2025-07-24 23:52:52,674 autoreload 3688 11240 Watching for file changes with StatReloader
INFO 2025-07-24 23:52:58,331 basehttp 16284 23804 "GET / HTTP/1.1" 302 0
INFO 2025-07-24 23:52:58,407 basehttp 16284 23804 "GET /login/?next=/ HTTP/1.1" 200 12878
INFO 2025-07-24 23:52:58,610 basehttp 16284 23804 "GET /static/css/dashboard.css HTTP/1.1" 304 0
INFO 2025-07-24 23:52:58,616 basehttp 16284 25152 "GET /static/js/dashboard.js HTTP/1.1" 304 0
INFO 2025-07-24 23:53:06,142 basehttp 16284 25152 "POST /login/?next=/ HTTP/1.1" 302 0
INFO 2025-07-24 23:53:06,156 middleware 16284 25152 New session started for user: lanzy from IP: 127.0.0.1
INFO 2025-07-24 23:53:06,202 basehttp 16284 25152 "GET / HTTP/1.1" 200 13248
INFO 2025-07-24 23:53:10,187 basehttp 16284 26200 "GET /supply-request/new/ HTTP/1.1" 200 16056
INFO 2025-07-24 23:53:12,531 basehttp 16284 26200 "GET /supply-request/ HTTP/1.1" 200 13283
INFO 2025-07-24 23:53:14,755 basehttp 16284 26200 "GET / HTTP/1.1" 200 13248
INFO 2025-07-24 23:53:16,018 basehttp 16284 26200 "GET /supply-request/new/ HTTP/1.1" 200 16056
INFO 2025-07-24 23:53:18,139 basehttp 16284 14624 "GET /supply-request/ HTTP/1.1" 200 13283
INFO 2025-07-24 23:55:19,414 autoreload 16284 4196 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-24 23:55:19,464 autoreload 3688 11240 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-24 23:55:19,901 autoreload 11396 8788 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-24 23:55:20,603 autoreload 15560 24424 Watching for file changes with StatReloader
INFO 2025-07-24 23:55:20,605 autoreload 11204 11808 Watching for file changes with StatReloader
INFO 2025-07-24 23:55:21,160 autoreload 25852 6964 Watching for file changes with StatReloader
INFO 2025-07-24 23:55:45,142 autoreload 25852 6964 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-24 23:55:45,265 autoreload 15560 24424 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-24 23:55:45,448 autoreload 11204 11808 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-24 23:55:46,231 autoreload 26420 2292 Watching for file changes with StatReloader
INFO 2025-07-24 23:55:46,284 autoreload 26416 14120 Watching for file changes with StatReloader
INFO 2025-07-24 23:55:46,554 autoreload 22484 9080 Watching for file changes with StatReloader
INFO 2025-07-24 23:55:55,854 autoreload 26420 2292 C:\Users\<USER>\dev\smartsupply\suptrack\urls.py changed, reloading.
INFO 2025-07-24 23:55:56,061 autoreload 22484 9080 C:\Users\<USER>\dev\smartsupply\suptrack\urls.py changed, reloading.
INFO 2025-07-24 23:55:56,120 autoreload 26416 14120 C:\Users\<USER>\dev\smartsupply\suptrack\urls.py changed, reloading.
INFO 2025-07-24 23:55:56,828 autoreload 21820 7868 Watching for file changes with StatReloader
INFO 2025-07-24 23:55:57,068 autoreload 21428 24456 Watching for file changes with StatReloader
INFO 2025-07-24 23:55:57,264 autoreload 23096 18912 Watching for file changes with StatReloader
INFO 2025-07-24 23:56:38,816 autoreload 23096 18912 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-24 23:56:39,196 autoreload 21428 24456 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-24 23:56:39,696 autoreload 21820 7868 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-24 23:56:39,866 autoreload 20000 22244 Watching for file changes with StatReloader
INFO 2025-07-24 23:56:40,183 autoreload 5492 26392 Watching for file changes with StatReloader
INFO 2025-07-24 23:56:40,565 autoreload 3916 21612 Watching for file changes with StatReloader
INFO 2025-07-24 23:56:57,752 autoreload 20000 22244 C:\Users\<USER>\dev\smartsupply\suptrack\models.py changed, reloading.
INFO 2025-07-24 23:56:57,828 autoreload 5492 26392 C:\Users\<USER>\dev\smartsupply\suptrack\models.py changed, reloading.
INFO 2025-07-24 23:56:58,131 autoreload 3916 21612 C:\Users\<USER>\dev\smartsupply\suptrack\models.py changed, reloading.
INFO 2025-07-24 23:56:58,869 autoreload 25176 16084 Watching for file changes with StatReloader
INFO 2025-07-24 23:56:58,920 autoreload 16528 23480 Watching for file changes with StatReloader
INFO 2025-07-24 23:56:59,126 autoreload 16376 8456 Watching for file changes with StatReloader
INFO 2025-07-24 23:57:12,470 autoreload 25176 16084 C:\Users\<USER>\dev\smartsupply\suptrack\models.py changed, reloading.
INFO 2025-07-24 23:57:12,620 autoreload 16376 8456 C:\Users\<USER>\dev\smartsupply\suptrack\models.py changed, reloading.
INFO 2025-07-24 23:57:12,796 autoreload 16528 23480 C:\Users\<USER>\dev\smartsupply\suptrack\models.py changed, reloading.
INFO 2025-07-24 23:57:13,592 autoreload 19696 24980 Watching for file changes with StatReloader
INFO 2025-07-24 23:57:13,703 autoreload 10528 16644 Watching for file changes with StatReloader
INFO 2025-07-24 23:57:14,079 autoreload 19728 17024 Watching for file changes with StatReloader
INFO 2025-07-24 23:58:26,523 autoreload 19696 24980 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-24 23:58:26,596 autoreload 19728 17024 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-24 23:58:27,343 autoreload 9500 24968 Watching for file changes with StatReloader
INFO 2025-07-24 23:58:27,501 autoreload 22384 7392 Watching for file changes with StatReloader
INFO 2025-07-24 23:58:37,193 autoreload 22384 7392 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-24 23:58:37,848 autoreload 9500 24968 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-24 23:58:38,322 autoreload 12968 21816 Watching for file changes with StatReloader
INFO 2025-07-24 23:58:38,917 autoreload 15336 13056 Watching for file changes with StatReloader
INFO 2025-07-24 23:58:49,866 autoreload 12968 21816 C:\Users\<USER>\dev\smartsupply\suptrack\forms.py changed, reloading.
INFO 2025-07-24 23:58:50,416 autoreload 15336 13056 C:\Users\<USER>\dev\smartsupply\suptrack\forms.py changed, reloading.
INFO 2025-07-24 23:58:51,026 autoreload 23408 13164 Watching for file changes with StatReloader
INFO 2025-07-24 23:58:51,265 autoreload 25252 2304 Watching for file changes with StatReloader
INFO 2025-07-25 00:01:03,763 autoreload 23408 13164 C:\Users\<USER>\dev\smartsupply\suptrack\models.py changed, reloading.
INFO 2025-07-25 00:01:03,819 autoreload 25252 2304 C:\Users\<USER>\dev\smartsupply\suptrack\models.py changed, reloading.
INFO 2025-07-25 00:01:04,689 autoreload 12636 26044 Watching for file changes with StatReloader
INFO 2025-07-25 00:01:04,813 autoreload 22224 23860 Watching for file changes with StatReloader
INFO 2025-07-25 00:01:37,690 autoreload 22224 23860 C:\Users\<USER>\dev\smartsupply\suptrack\models.py changed, reloading.
INFO 2025-07-25 00:01:38,449 autoreload 8776 21924 Watching for file changes with StatReloader
INFO 2025-07-25 00:01:38,504 autoreload 12636 26044 C:\Users\<USER>\dev\smartsupply\suptrack\models.py changed, reloading.
INFO 2025-07-25 00:01:39,004 autoreload 5532 16088 Watching for file changes with StatReloader
INFO 2025-07-25 00:02:38,989 autoreload 8776 21924 C:\Users\<USER>\dev\smartsupply\suptrack\models.py changed, reloading.
INFO 2025-07-25 00:02:39,096 autoreload 5532 16088 C:\Users\<USER>\dev\smartsupply\suptrack\models.py changed, reloading.
INFO 2025-07-25 00:02:40,492 autoreload 24872 16892 Watching for file changes with StatReloader
INFO 2025-07-25 00:02:40,576 autoreload 5432 25572 Watching for file changes with StatReloader
INFO 2025-07-25 00:02:54,378 autoreload 24872 16892 C:\Users\<USER>\dev\smartsupply\suptrack\models.py changed, reloading.
INFO 2025-07-25 00:02:54,629 autoreload 5432 25572 C:\Users\<USER>\dev\smartsupply\suptrack\models.py changed, reloading.
INFO 2025-07-25 00:02:55,573 autoreload 23624 16172 Watching for file changes with StatReloader
INFO 2025-07-25 00:02:56,155 autoreload 3700 2276 Watching for file changes with StatReloader
INFO 2025-07-25 00:03:14,372 autoreload 23624 16172 C:\Users\<USER>\dev\smartsupply\suptrack\admin.py changed, reloading.
INFO 2025-07-25 00:03:14,993 autoreload 3700 2276 C:\Users\<USER>\dev\smartsupply\suptrack\admin.py changed, reloading.
INFO 2025-07-25 00:03:15,058 autoreload 15404 15140 Watching for file changes with StatReloader
INFO 2025-07-25 00:03:16,182 autoreload 8268 24548 Watching for file changes with StatReloader
INFO 2025-07-25 00:05:38,962 autoreload 15404 15140 C:\Users\<USER>\dev\smartsupply\suptrack\migrations\0001_initial.py changed, reloading.
INFO 2025-07-25 00:05:39,246 autoreload 8268 24548 C:\Users\<USER>\dev\smartsupply\suptrack\migrations\0001_initial.py changed, reloading.
INFO 2025-07-25 00:05:39,904 autoreload 3916 24288 Watching for file changes with StatReloader
INFO 2025-07-25 00:05:40,183 autoreload 23176 24636 Watching for file changes with StatReloader
INFO 2025-07-25 00:06:15,711 autoreload 3916 24288 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:06:16,255 autoreload 23176 24636 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:06:16,713 autoreload 16892 11836 Watching for file changes with StatReloader
INFO 2025-07-25 00:06:17,346 autoreload 1776 16268 Watching for file changes with StatReloader
INFO 2025-07-25 00:06:33,071 autoreload 1776 16268 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:06:33,241 autoreload 16892 11836 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:06:34,235 autoreload 4480 24768 Watching for file changes with StatReloader
INFO 2025-07-25 00:06:34,250 autoreload 23316 25896 Watching for file changes with StatReloader
INFO 2025-07-25 00:06:39,649 autoreload 23316 25896 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:06:39,726 autoreload 4480 24768 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:06:40,581 autoreload 5484 25436 Watching for file changes with StatReloader
INFO 2025-07-25 00:06:40,829 autoreload 24420 11944 Watching for file changes with StatReloader
INFO 2025-07-25 00:07:18,498 autoreload 5484 25436 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:07:19,217 autoreload 24420 11944 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:07:19,323 autoreload 21120 12020 Watching for file changes with StatReloader
INFO 2025-07-25 00:07:20,586 autoreload 10528 26564 Watching for file changes with StatReloader
INFO 2025-07-25 00:07:29,000 autoreload 21120 12020 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:07:29,134 autoreload 10528 26564 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:07:30,146 autoreload 10080 14456 Watching for file changes with StatReloader
INFO 2025-07-25 00:07:30,475 autoreload 11032 17740 Watching for file changes with StatReloader
INFO 2025-07-25 00:07:38,010 autoreload 11032 17740 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:07:38,681 autoreload 10080 14456 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:07:38,849 autoreload 6720 25152 Watching for file changes with StatReloader
INFO 2025-07-25 00:07:39,715 autoreload 2912 5836 Watching for file changes with StatReloader
INFO 2025-07-25 00:08:32,498 autoreload 6720 25152 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:08:32,942 autoreload 2912 5836 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:08:33,224 autoreload 11500 5132 Watching for file changes with StatReloader
INFO 2025-07-25 00:08:34,065 autoreload 24796 3024 Watching for file changes with StatReloader
INFO 2025-07-25 00:08:59,763 autoreload 24796 3024 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:09:00,177 autoreload 11500 5132 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:09:00,875 autoreload 24236 23396 Watching for file changes with StatReloader
INFO 2025-07-25 00:09:01,407 autoreload 25484 19880 Watching for file changes with StatReloader
INFO 2025-07-25 00:09:23,320 autoreload 25484 19880 C:\Users\<USER>\dev\smartsupply\suptrack\urls.py changed, reloading.
INFO 2025-07-25 00:09:23,508 autoreload 24236 23396 C:\Users\<USER>\dev\smartsupply\suptrack\urls.py changed, reloading.
INFO 2025-07-25 00:09:23,994 autoreload 7484 5900 Watching for file changes with StatReloader
INFO 2025-07-25 00:09:24,167 autoreload 24196 22168 Watching for file changes with StatReloader
INFO 2025-07-25 00:10:57,370 autoreload 24196 22168 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:10:57,895 autoreload 7484 5900 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:10:59,325 autoreload 9996 24784 Watching for file changes with StatReloader
INFO 2025-07-25 00:11:00,072 autoreload 6748 12644 Watching for file changes with StatReloader
INFO 2025-07-25 00:13:24,441 autoreload 9996 24784 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:13:25,173 autoreload 192 15692 Watching for file changes with StatReloader
INFO 2025-07-25 00:13:25,201 autoreload 6748 12644 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:13:25,863 autoreload 8996 4764 Watching for file changes with StatReloader
INFO 2025-07-25 00:14:04,064 autoreload 192 15692 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:14:04,229 autoreload 8996 4764 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:14:04,970 autoreload 21692 24880 Watching for file changes with StatReloader
INFO 2025-07-25 00:14:05,324 autoreload 12636 23228 Watching for file changes with StatReloader
INFO 2025-07-25 00:14:10,464 autoreload 21692 24880 C:\Users\<USER>\dev\smartsupply\suptrack\urls.py changed, reloading.
INFO 2025-07-25 00:14:10,863 autoreload 12636 23228 C:\Users\<USER>\dev\smartsupply\suptrack\urls.py changed, reloading.
INFO 2025-07-25 00:14:11,160 autoreload 13068 22832 Watching for file changes with StatReloader
INFO 2025-07-25 00:14:11,986 autoreload 1776 17380 Watching for file changes with StatReloader
INFO 2025-07-25 00:16:18,485 autoreload 1776 17380 C:\Users\<USER>\dev\smartsupply\suptrack\models.py changed, reloading.
INFO 2025-07-25 00:16:19,256 autoreload 13068 22832 C:\Users\<USER>\dev\smartsupply\suptrack\models.py changed, reloading.
INFO 2025-07-25 00:16:19,967 autoreload 24016 23216 Watching for file changes with StatReloader
INFO 2025-07-25 00:16:20,559 autoreload 12780 17404 Watching for file changes with StatReloader
INFO 2025-07-25 00:16:42,921 autoreload 24016 23216 C:\Users\<USER>\dev\smartsupply\suptrack\admin.py changed, reloading.
INFO 2025-07-25 00:16:43,117 autoreload 12780 17404 C:\Users\<USER>\dev\smartsupply\suptrack\admin.py changed, reloading.
INFO 2025-07-25 00:16:43,697 autoreload 25092 24728 Watching for file changes with StatReloader
INFO 2025-07-25 00:16:43,918 autoreload 23144 18952 Watching for file changes with StatReloader
INFO 2025-07-25 00:16:56,409 autoreload 25092 24728 C:\Users\<USER>\dev\smartsupply\suptrack\admin.py changed, reloading.
INFO 2025-07-25 00:16:56,551 autoreload 23144 18952 C:\Users\<USER>\dev\smartsupply\suptrack\admin.py changed, reloading.
INFO 2025-07-25 00:16:58,087 autoreload 12548 5444 Watching for file changes with StatReloader
INFO 2025-07-25 00:16:58,348 autoreload 15892 7764 Watching for file changes with StatReloader
INFO 2025-07-25 00:19:51,442 autoreload 15892 7764 C:\Users\<USER>\dev\smartsupply\suptrack\models.py changed, reloading.
INFO 2025-07-25 00:19:51,730 autoreload 12548 5444 C:\Users\<USER>\dev\smartsupply\suptrack\models.py changed, reloading.
INFO 2025-07-25 00:19:52,995 autoreload 7392 2520 Watching for file changes with StatReloader
INFO 2025-07-25 00:19:53,214 autoreload 23228 16228 Watching for file changes with StatReloader
INFO 2025-07-25 00:20:35,622 autoreload 7392 2520 C:\Users\<USER>\dev\smartsupply\suptrack\apps.py changed, reloading.
INFO 2025-07-25 00:20:36,291 autoreload 23228 16228 C:\Users\<USER>\dev\smartsupply\suptrack\apps.py changed, reloading.
INFO 2025-07-25 00:20:36,470 autoreload 10080 23616 Watching for file changes with StatReloader
INFO 2025-07-25 00:20:37,321 autoreload 5280 25648 Watching for file changes with StatReloader
INFO 2025-07-25 00:20:46,741 autoreload 5280 25648 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:20:47,046 autoreload 10080 23616 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:20:48,012 autoreload 15992 21136 Watching for file changes with StatReloader
INFO 2025-07-25 00:20:48,543 autoreload 11252 25088 Watching for file changes with StatReloader
INFO 2025-07-25 00:21:03,154 autoreload 11252 25088 C:\Users\<USER>\dev\smartsupply\smartsupply\settings.py changed, reloading.
INFO 2025-07-25 00:21:03,577 autoreload 15992 21136 C:\Users\<USER>\dev\smartsupply\smartsupply\settings.py changed, reloading.
INFO 2025-07-25 00:21:04,232 autoreload 15176 23896 Watching for file changes with StatReloader
INFO 2025-07-25 00:21:04,778 autoreload 7920 5852 Watching for file changes with StatReloader
INFO 2025-07-25 00:21:29,299 autoreload 15176 23896 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:21:29,507 autoreload 7920 5852 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:21:30,446 autoreload 20996 10768 Watching for file changes with StatReloader
INFO 2025-07-25 00:21:30,530 autoreload 21240 19044 Watching for file changes with StatReloader
INFO 2025-07-25 00:21:43,917 autoreload 21240 19044 C:\Users\<USER>\dev\smartsupply\suptrack\urls.py changed, reloading.
INFO 2025-07-25 00:21:44,043 autoreload 20996 10768 C:\Users\<USER>\dev\smartsupply\suptrack\urls.py changed, reloading.
INFO 2025-07-25 00:21:44,893 autoreload 15996 25096 Watching for file changes with StatReloader
INFO 2025-07-25 00:21:45,146 autoreload 26064 15772 Watching for file changes with StatReloader
INFO 2025-07-25 00:22:04,585 autoreload 15996 25096 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:22:04,958 autoreload 26064 15772 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:22:05,742 autoreload 13872 16376 Watching for file changes with StatReloader
INFO 2025-07-25 00:22:06,305 autoreload 16216 24348 Watching for file changes with StatReloader
INFO 2025-07-25 00:22:12,773 autoreload 16216 24348 C:\Users\<USER>\dev\smartsupply\suptrack\urls.py changed, reloading.
INFO 2025-07-25 00:22:13,214 autoreload 13872 16376 C:\Users\<USER>\dev\smartsupply\suptrack\urls.py changed, reloading.
INFO 2025-07-25 00:22:13,803 autoreload 19128 14116 Watching for file changes with StatReloader
INFO 2025-07-25 00:22:14,200 autoreload 3960 13232 Watching for file changes with StatReloader
INFO 2025-07-25 00:22:25,407 autoreload 19128 14116 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:22:25,604 autoreload 3960 13232 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:22:26,480 autoreload 24252 25828 Watching for file changes with StatReloader
INFO 2025-07-25 00:22:26,588 autoreload 12724 7840 Watching for file changes with StatReloader
INFO 2025-07-25 00:22:31,980 autoreload 24252 25828 C:\Users\<USER>\dev\smartsupply\suptrack\urls.py changed, reloading.
INFO 2025-07-25 00:22:32,064 autoreload 12724 7840 C:\Users\<USER>\dev\smartsupply\suptrack\urls.py changed, reloading.
INFO 2025-07-25 00:22:32,714 autoreload 10076 24152 Watching for file changes with StatReloader
INFO 2025-07-25 00:22:32,782 autoreload 22016 6752 Watching for file changes with StatReloader
INFO 2025-07-25 00:23:08,485 autoreload 10076 24152 C:\Users\<USER>\dev\smartsupply\suptrack\forms.py changed, reloading.
INFO 2025-07-25 00:23:08,839 autoreload 22016 6752 C:\Users\<USER>\dev\smartsupply\suptrack\forms.py changed, reloading.
INFO 2025-07-25 00:23:09,289 autoreload 22596 15612 Watching for file changes with StatReloader
INFO 2025-07-25 00:23:10,166 autoreload 23296 25968 Watching for file changes with StatReloader
INFO 2025-07-25 00:23:24,801 autoreload 23296 25968 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:23:25,027 autoreload 22596 15612 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:23:25,873 autoreload 17608 10444 Watching for file changes with StatReloader
INFO 2025-07-25 00:23:26,129 autoreload 25100 23768 Watching for file changes with StatReloader
INFO 2025-07-25 00:23:34,804 autoreload 25100 23768 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:23:35,533 autoreload 8468 7372 Watching for file changes with StatReloader
INFO 2025-07-25 00:23:35,633 autoreload 17608 10444 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:23:36,642 autoreload 18912 16848 Watching for file changes with StatReloader
INFO 2025-07-25 00:23:56,278 autoreload 8468 7372 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:23:56,554 autoreload 18912 16848 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:23:57,334 autoreload 22868 22960 Watching for file changes with StatReloader
INFO 2025-07-25 00:23:57,692 autoreload 13524 3212 Watching for file changes with StatReloader
INFO 2025-07-25 00:24:01,688 autoreload 22868 22960 C:\Users\<USER>\dev\smartsupply\suptrack\urls.py changed, reloading.
INFO 2025-07-25 00:24:02,041 autoreload 13524 3212 C:\Users\<USER>\dev\smartsupply\suptrack\urls.py changed, reloading.
INFO 2025-07-25 00:24:02,339 autoreload 20076 23232 Watching for file changes with StatReloader
INFO 2025-07-25 00:24:03,096 autoreload 1868 8772 Watching for file changes with StatReloader
INFO 2025-07-25 00:24:48,371 autoreload 20076 23232 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:24:48,602 autoreload 1868 8772 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:24:49,576 autoreload 9848 16204 Watching for file changes with StatReloader
INFO 2025-07-25 00:24:50,022 autoreload 24428 7096 Watching for file changes with StatReloader
INFO 2025-07-25 00:25:13,933 autoreload 24428 7096 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:25:14,302 autoreload 9848 16204 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 00:25:15,467 autoreload 7064 15140 Watching for file changes with StatReloader
INFO 2025-07-25 00:25:15,847 autoreload 2108 18680 Watching for file changes with StatReloader
INFO 2025-07-25 00:25:20,180 autoreload 2108 18680 C:\Users\<USER>\dev\smartsupply\suptrack\urls.py changed, reloading.
INFO 2025-07-25 00:25:20,753 autoreload 17668 2988 Watching for file changes with StatReloader
INFO 2025-07-25 00:25:20,965 autoreload 7064 15140 C:\Users\<USER>\dev\smartsupply\suptrack\urls.py changed, reloading.
INFO 2025-07-25 00:25:22,029 autoreload 23148 23124 Watching for file changes with StatReloader
INFO 2025-07-25 02:12:24,110 autoreload 26788 26792 Watching for file changes with StatReloader
INFO 2025-07-25 02:12:27,286 basehttp 17668 26976 "GET / HTTP/1.1" 302 0
INFO 2025-07-25 02:12:27,557 basehttp 17668 26976 "GET /login/?next=/ HTTP/1.1" 200 13742
INFO 2025-07-25 02:12:27,673 basehttp 17668 26976 "GET /static/css/dashboard.css HTTP/1.1" 304 0
INFO 2025-07-25 02:12:27,674 basehttp 17668 23660 "GET /static/js/dashboard.js HTTP/1.1" 304 0
INFO 2025-07-25 02:12:36,660 basehttp 17668 23660 "POST /login/?next=/ HTTP/1.1" 302 0
INFO 2025-07-25 02:12:36,672 middleware 17668 23660 New session started for user: lanzy from IP: 127.0.0.1
ERROR 2025-07-25 02:12:36,677 middleware 17668 23660 Exception for user lanzy: FieldError: Cannot resolve keyword 'request_date' into field. Choices are: approved_by, approved_by_id, approved_date, department, id, justification, notes, rejection_reason, request_id, request_items, requested_date, requester, requester_id, required_date, status
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\views.py", line 70, in dashboard
    user_requests = SupplyRequest.objects.filter(requester=request.user).order_by('-request_date')
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\models\query.py", line 1722, in order_by
    obj.query.add_ordering(*field_names)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\models\sql\query.py", line 2291, in add_ordering
    self.names_to_path(item.split(LOOKUP_SEP), self.model._meta)
    ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\models\sql\query.py", line 1805, in names_to_path
    raise FieldError(
    ...<2 lines>...
    )
django.core.exceptions.FieldError: Cannot resolve keyword 'request_date' into field. Choices are: approved_by, approved_by_id, approved_date, department, id, justification, notes, rejection_reason, request_id, request_items, requested_date, requester, requester_id, required_date, status
ERROR 2025-07-25 02:12:36,984 log 17668 23660 Internal Server Error: /
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\views.py", line 70, in dashboard
    user_requests = SupplyRequest.objects.filter(requester=request.user).order_by('-request_date')
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\models\query.py", line 1722, in order_by
    obj.query.add_ordering(*field_names)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\models\sql\query.py", line 2291, in add_ordering
    self.names_to_path(item.split(LOOKUP_SEP), self.model._meta)
    ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\models\sql\query.py", line 1805, in names_to_path
    raise FieldError(
    ...<2 lines>...
    )
django.core.exceptions.FieldError: Cannot resolve keyword 'request_date' into field. Choices are: approved_by, approved_by_id, approved_date, department, id, justification, notes, rejection_reason, request_id, request_items, requested_date, requester, requester_id, required_date, status
ERROR 2025-07-25 02:12:36,991 basehttp 17668 23660 "GET / HTTP/1.1" 500 91959
INFO 2025-07-25 02:20:34,667 middleware 17668 10572 New session started for user: lanzy from IP: 127.0.0.1
ERROR 2025-07-25 02:20:34,674 middleware 17668 10572 Exception for user lanzy: NoReverseMatch: Reverse for 'password_reset' not found. 'password_reset' is not a valid view function or pattern name.
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\views.py", line 49, in login_view
    return render(request, 'registration/login.html', {'form': form})
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'password_reset' not found. 'password_reset' is not a valid view function or pattern name.
ERROR 2025-07-25 02:20:34,943 log 17668 10572 Internal Server Error: /login/
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\views.py", line 49, in login_view
    return render(request, 'registration/login.html', {'form': form})
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'password_reset' not found. 'password_reset' is not a valid view function or pattern name.
ERROR 2025-07-25 02:20:34,946 basehttp 17668 10572 "GET /login/?next=/ HTTP/1.1" 500 197890
INFO 2025-07-25 02:21:26,035 autoreload 26788 26792 C:\Users\<USER>\dev\smartsupply\suptrack\urls.py changed, reloading.
INFO 2025-07-25 02:21:26,058 autoreload 23148 23124 C:\Users\<USER>\dev\smartsupply\suptrack\urls.py changed, reloading.
INFO 2025-07-25 02:21:26,311 autoreload 17668 2988 C:\Users\<USER>\dev\smartsupply\suptrack\urls.py changed, reloading.
INFO 2025-07-25 02:21:26,943 autoreload 6796 15104 Watching for file changes with StatReloader
INFO 2025-07-25 02:21:27,192 autoreload 28416 26700 Watching for file changes with StatReloader
INFO 2025-07-25 02:21:27,685 autoreload 2752 25348 Watching for file changes with StatReloader
INFO 2025-07-25 02:21:45,415 autoreload 2752 25348 C:\Users\<USER>\dev\smartsupply\suptrack\urls.py changed, reloading.
INFO 2025-07-25 02:21:45,419 autoreload 6796 15104 C:\Users\<USER>\dev\smartsupply\suptrack\urls.py changed, reloading.
INFO 2025-07-25 02:21:45,603 autoreload 28416 26700 C:\Users\<USER>\dev\smartsupply\suptrack\urls.py changed, reloading.
INFO 2025-07-25 02:21:46,293 autoreload 15120 9704 Watching for file changes with StatReloader
INFO 2025-07-25 02:21:46,420 autoreload 13648 7208 Watching for file changes with StatReloader
INFO 2025-07-25 02:21:46,460 autoreload 6088 20000 Watching for file changes with StatReloader
INFO 2025-07-25 02:22:52,431 middleware 15120 26556 New session started for user: lanzy from IP: 127.0.0.1
ERROR 2025-07-25 02:22:52,474 middleware 15120 26556 Exception for user lanzy: NoReverseMatch: Reverse for 'password_reset' not found. 'password_reset' is not a valid view function or pattern name.
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\views.py", line 49, in login_view
    return render(request, 'registration/login.html', {'form': form})
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'password_reset' not found. 'password_reset' is not a valid view function or pattern name.
ERROR 2025-07-25 02:22:52,521 log 15120 26556 Internal Server Error: /login/
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\views.py", line 49, in login_view
    return render(request, 'registration/login.html', {'form': form})
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'password_reset' not found. 'password_reset' is not a valid view function or pattern name.
ERROR 2025-07-25 02:22:52,524 basehttp 15120 26556 "GET /login/?next=/ HTTP/1.1" 500 197795
INFO 2025-07-25 02:22:54,538 middleware 15120 26556 New session started for user: lanzy from IP: 127.0.0.1
ERROR 2025-07-25 02:22:54,541 middleware 15120 26556 Exception for user lanzy: NoReverseMatch: Reverse for 'password_reset' not found. 'password_reset' is not a valid view function or pattern name.
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\views.py", line 49, in login_view
    return render(request, 'registration/login.html', {'form': form})
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'password_reset' not found. 'password_reset' is not a valid view function or pattern name.
ERROR 2025-07-25 02:22:54,574 log 15120 26556 Internal Server Error: /login/
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\views.py", line 49, in login_view
    return render(request, 'registration/login.html', {'form': form})
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'password_reset' not found. 'password_reset' is not a valid view function or pattern name.
ERROR 2025-07-25 02:22:54,578 basehttp 15120 26556 "GET /login/?next=/ HTTP/1.1" 500 197795
INFO 2025-07-25 02:23:35,064 autoreload 15784 6608 Watching for file changes with StatReloader
INFO 2025-07-25 02:23:56,123 basehttp 15120 20204 "GET / HTTP/1.1" 302 0
ERROR 2025-07-25 02:23:56,142 middleware 15120 20204 Exception for user : NoReverseMatch: Reverse for 'password_reset' not found. 'password_reset' is not a valid view function or pattern name.
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\views.py", line 49, in login_view
    return render(request, 'registration/login.html', {'form': form})
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'password_reset' not found. 'password_reset' is not a valid view function or pattern name.
ERROR 2025-07-25 02:23:56,218 log 15120 20204 Internal Server Error: /login/
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\views.py", line 49, in login_view
    return render(request, 'registration/login.html', {'form': form})
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'password_reset' not found. 'password_reset' is not a valid view function or pattern name.
ERROR 2025-07-25 02:23:56,222 basehttp 15120 20204 "GET /login/?next=/ HTTP/1.1" 500 197390
WARNING 2025-07-25 02:23:56,484 log 15120 20204 Not Found: /favicon.ico
WARNING 2025-07-25 02:23:56,485 basehttp 15120 20204 "GET /favicon.ico HTTP/1.1" 404 8721
INFO 2025-07-25 02:24:57,637 basehttp 15120 9012 "GET /login/?next=/ HTTP/1.1" 200 14412
INFO 2025-07-25 02:24:57,665 basehttp 15120 9012 "GET /static/css/dashboard.css HTTP/1.1" 304 0
INFO 2025-07-25 02:24:57,666 basehttp 15120 27752 "GET /static/js/dashboard.js HTTP/1.1" 304 0
INFO 2025-07-25 02:25:09,537 autoreload 5600 14728 Watching for file changes with StatReloader
INFO 2025-07-25 02:27:04,032 autoreload 428 21756 Watching for file changes with StatReloader
INFO 2025-07-25 02:27:08,378 basehttp 13648 24232 "GET /login/?next=/ HTTP/1.1" 200 13552
INFO 2025-07-25 02:27:22,817 basehttp 13648 24232 "POST /login/?next=/ HTTP/1.1" 302 0
INFO 2025-07-25 02:27:22,823 middleware 13648 24232 New session started for user: lanzy from IP: 127.0.0.1
ERROR 2025-07-25 02:27:22,825 middleware 13648 24232 Exception for user lanzy: FieldError: Cannot resolve keyword 'request_date' into field. Choices are: approved_by, approved_by_id, approved_date, department, id, justification, notes, rejection_reason, request_id, request_items, requested_date, requester, requester_id, required_date, status
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\views.py", line 70, in dashboard
    user_requests = SupplyRequest.objects.filter(requester=request.user).order_by('-request_date')
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\models\query.py", line 1722, in order_by
    obj.query.add_ordering(*field_names)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\models\sql\query.py", line 2291, in add_ordering
    self.names_to_path(item.split(LOOKUP_SEP), self.model._meta)
    ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\models\sql\query.py", line 1805, in names_to_path
    raise FieldError(
    ...<2 lines>...
    )
django.core.exceptions.FieldError: Cannot resolve keyword 'request_date' into field. Choices are: approved_by, approved_by_id, approved_date, department, id, justification, notes, rejection_reason, request_id, request_items, requested_date, requester, requester_id, required_date, status
ERROR 2025-07-25 02:27:22,865 log 13648 24232 Internal Server Error: /
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\views.py", line 70, in dashboard
    user_requests = SupplyRequest.objects.filter(requester=request.user).order_by('-request_date')
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\models\query.py", line 1722, in order_by
    obj.query.add_ordering(*field_names)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\models\sql\query.py", line 2291, in add_ordering
    self.names_to_path(item.split(LOOKUP_SEP), self.model._meta)
    ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\db\models\sql\query.py", line 1805, in names_to_path
    raise FieldError(
    ...<2 lines>...
    )
django.core.exceptions.FieldError: Cannot resolve keyword 'request_date' into field. Choices are: approved_by, approved_by_id, approved_date, department, id, justification, notes, rejection_reason, request_id, request_items, requested_date, requester, requester_id, required_date, status
ERROR 2025-07-25 02:27:22,867 basehttp 13648 24232 "GET / HTTP/1.1" 500 91839
INFO 2025-07-25 02:27:58,615 autoreload 6088 20000 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 02:27:59,102 autoreload 428 21756 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 02:27:59,427 autoreload 13648 7208 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 02:27:59,746 autoreload 27740 27628 Watching for file changes with StatReloader
INFO 2025-07-25 02:28:00,054 autoreload 25376 27004 Watching for file changes with StatReloader
INFO 2025-07-25 02:28:00,551 autoreload 24184 27188 Watching for file changes with StatReloader
INFO 2025-07-25 02:28:17,849 autoreload 24184 27188 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 02:28:18,363 autoreload 27740 27628 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 02:28:18,440 autoreload 28308 24676 Watching for file changes with StatReloader
INFO 2025-07-25 02:28:18,596 autoreload 25376 27004 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 02:28:19,292 autoreload 13612 24996 Watching for file changes with StatReloader
INFO 2025-07-25 02:28:19,349 autoreload 28100 28268 Watching for file changes with StatReloader
INFO 2025-07-25 02:28:33,007 autoreload 15252 200 Watching for file changes with StatReloader
INFO 2025-07-25 02:28:38,208 middleware 28308 14900 New session started for user: lanzy from IP: 127.0.0.1
INFO 2025-07-25 02:28:38,237 basehttp 28308 14900 "GET / HTTP/1.1" 200 12688
INFO 2025-07-25 02:28:48,006 basehttp 28308 8096 "GET /supply-request/new/ HTTP/1.1" 200 16434
INFO 2025-07-25 02:28:50,638 basehttp 28308 8096 "GET /supply-request/ HTTP/1.1" 200 13287
INFO 2025-07-25 02:30:38,031 basehttp 28308 23152 "GET /supply-request/ HTTP/1.1" 200 13810
INFO 2025-07-25 02:36:02,674 basehttp 28308 27084 "GET /supply-request/ HTTP/1.1" 200 13287
INFO 2025-07-25 02:36:55,173 basehttp 28308 27084 "GET /supply-request/ HTTP/1.1" 200 13287
INFO 2025-07-25 02:37:33,492 middleware 28308 26724 New session started for user: lanzy from IP: 127.0.0.1
INFO 2025-07-25 02:37:33,504 basehttp 28308 26724 "GET /login/?next=/ HTTP/1.1" 200 14668
INFO 2025-07-25 02:37:37,502 basehttp 28308 26724 "GET /register/ HTTP/1.1" 200 16547
INFO 2025-07-25 02:37:37,549 basehttp 28308 26724 "GET /static/css/dashboard.css HTTP/1.1" 304 0
INFO 2025-07-25 02:37:37,551 basehttp 28308 3016 "GET /static/js/dashboard.js HTTP/1.1" 304 0
INFO 2025-07-25 02:37:40,752 basehttp 28308 3016 "GET /login/ HTTP/1.1" 200 14668
INFO 2025-07-25 02:37:45,946 basehttp 28308 3016 "POST /login/ HTTP/1.1" 302 0
INFO 2025-07-25 02:37:45,981 basehttp 28308 3016 "GET / HTTP/1.1" 200 12688
INFO 2025-07-25 02:37:53,210 basehttp 28308 3016 "GET /supply-request/new/ HTTP/1.1" 200 16434
INFO 2025-07-25 12:41:37,046 autoreload 26248 4564 Watching for file changes with StatReloader
INFO 2025-07-25 12:41:39,683 basehttp 26248 8420 "GET / HTTP/1.1" 302 0
INFO 2025-07-25 12:41:39,707 basehttp 26248 8420 "GET /login/?next=/ HTTP/1.1" 200 13552
INFO 2025-07-25 12:41:39,819 basehttp 26248 24040 "GET /static/js/dashboard.js HTTP/1.1" 304 0
INFO 2025-07-25 12:41:39,819 basehttp 26248 8420 "GET /static/css/dashboard.css HTTP/1.1" 304 0
WARNING 2025-07-25 12:41:40,556 log 26248 8420 Not Found: /favicon.ico
WARNING 2025-07-25 12:41:40,557 basehttp 26248 8420 "GET /favicon.ico HTTP/1.1" 404 8721
INFO 2025-07-25 12:41:46,997 basehttp 26248 8420 "POST /login/?next=/ HTTP/1.1" 302 0
INFO 2025-07-25 12:41:47,014 middleware 26248 8420 New session started for user: lanzy from IP: 127.0.0.1
INFO 2025-07-25 12:41:47,055 basehttp 26248 8420 "GET / HTTP/1.1" 200 12688
INFO 2025-07-25 12:41:49,666 basehttp 26248 8420 "GET /supply-request/new/ HTTP/1.1" 200 16434
INFO 2025-07-25 12:41:51,611 basehttp 26248 22068 "GET /supply-request/ HTTP/1.1" 200 13287
INFO 2025-07-25 12:41:53,085 basehttp 26248 22068 "GET /logout/ HTTP/1.1" 302 0
INFO 2025-07-25 12:41:53,094 basehttp 26248 22068 "GET /login/ HTTP/1.1" 200 13552
INFO 2025-07-25 12:41:57,395 basehttp 26248 22068 "GET /register/ HTTP/1.1" 200 15431
INFO 2025-07-25 12:42:29,940 basehttp 26248 28388 "POST /register/ HTTP/1.1" 200 15579
INFO 2025-07-25 12:42:52,611 basehttp 26248 12128 "GET /login/ HTTP/1.1" 200 13552
INFO 2025-07-25 12:46:36,334 basehttp 26248 6884 "POST /login/ HTTP/1.1" 302 0
INFO 2025-07-25 12:46:36,347 middleware 26248 6884 New session started for user: lanzy from IP: 127.0.0.1
INFO 2025-07-25 12:46:36,380 basehttp 26248 6884 "GET / HTTP/1.1" 200 12688
INFO 2025-07-25 12:46:39,886 basehttp 26248 6884 "GET / HTTP/1.1" 200 12688
INFO 2025-07-25 12:46:41,023 basehttp 26248 6884 "GET / HTTP/1.1" 200 12688
INFO 2025-07-25 12:46:44,549 basehttp 26248 6884 "GET /supply-request/new/ HTTP/1.1" 200 16434
INFO 2025-07-25 12:47:22,528 autoreload 12104 7404 Watching for file changes with StatReloader
INFO 2025-07-25 12:47:24,479 basehttp 26248 27148 "GET / HTTP/1.1" 200 12688
INFO 2025-07-25 13:03:18,312 basehttp 26248 7524 "GET / HTTP/1.1" 200 24720
INFO 2025-07-25 13:03:21,488 basehttp 26248 7524 "GET /supply-request/new/ HTTP/1.1" 200 18443
INFO 2025-07-25 13:03:22,981 basehttp 26248 7524 "GET /supply-request/ HTTP/1.1" 200 15296
INFO 2025-07-25 13:03:25,588 basehttp 26248 7524 "GET / HTTP/1.1" 200 24720
INFO 2025-07-25 13:03:28,197 basehttp 26248 7524 "GET /supply-request/new/ HTTP/1.1" 200 18443
INFO 2025-07-25 13:03:30,213 basehttp 26248 7524 "GET /supply-request/new/ HTTP/1.1" 200 18443
INFO 2025-07-25 13:03:31,190 basehttp 26248 7524 "GET /supply-request/ HTTP/1.1" 200 15296
INFO 2025-07-25 13:03:35,321 basehttp 26248 7524 "GET /supply-request/new/ HTTP/1.1" 200 18443
INFO 2025-07-25 13:26:59,798 basehttp 26248 11144 "GET /supply-request/new/ HTTP/1.1" 200 36525
INFO 2025-07-25 13:27:12,074 basehttp 26248 11144 "GET /supply-request/ HTTP/1.1" 200 29249
INFO 2025-07-25 13:27:14,456 basehttp 26248 11144 "GET /supply-request/new/ HTTP/1.1" 200 36525
INFO 2025-07-25 13:29:39,047 autoreload 26248 4564 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 13:29:39,194 autoreload 12104 7404 C:\Users\<USER>\dev\smartsupply\suptrack\views.py changed, reloading.
INFO 2025-07-25 13:29:41,241 autoreload 28416 1896 Watching for file changes with StatReloader
INFO 2025-07-25 13:29:41,241 autoreload 20108 21316 Watching for file changes with StatReloader
ERROR 2025-07-25 13:48:52,433 manage_users 5628 25376 Command failed: User 'admin' already exists
ERROR 2025-07-25 13:48:53,190 manage_users 22600 8176 Command failed: File 'users.csv' does not exist
ERROR 2025-07-25 13:54:23,130 manage_inventory 26512 7804 Command failed: '<=' not supported between instances of 'int' and 'str'
INFO 2025-07-25 13:54:45,760 autoreload 8848 9500 Watching for file changes with StatReloader
INFO 2025-07-25 13:54:49,578 basehttp 28416 9388 "GET /supply-request/new/ HTTP/1.1" 200 36948
INFO 2025-07-25 13:54:53,678 basehttp 28416 9388 "GET /supply-request/ HTTP/1.1" 200 29249
INFO 2025-07-25 13:54:56,963 basehttp 28416 9388 "GET /supply-request/new/ HTTP/1.1" 200 36948
ERROR 2025-07-25 13:54:58,282 manage_inventory 8084 24864 Command failed: '<=' not supported between instances of 'int' and 'str'
INFO 2025-07-25 13:55:33,660 basehttp 28416 21028 "POST /supply-request/new/ HTTP/1.1" 302 0
INFO 2025-07-25 13:55:33,703 basehttp 28416 21028 "GET /supply-request/10/ HTTP/1.1" 200 24814
INFO 2025-07-25 13:55:44,339 basehttp 28416 25408 "GET /supply-request/ HTTP/1.1" 200 36505
INFO 2025-07-25 13:55:47,814 basehttp 28416 25408 "GET /supply-request/10/ HTTP/1.1" 200 24814
INFO 2025-07-25 13:55:49,556 basehttp 28416 25408 "GET /supply-request/ HTTP/1.1" 200 36505
INFO 2025-07-25 13:55:56,678 basehttp 28416 27056 "GET /logout/ HTTP/1.1" 302 0
ERROR 2025-07-25 13:55:56,692 middleware 28416 27056 Exception for user : VariableDoesNotExist: Failed lookup for key [0] in ''
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 891, in _resolve_lookup
    current = current[bit]
              ~~~~~~~^^^^^
TypeError: string indices must be integers, not 'str'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 901, in _resolve_lookup
    current = getattr(current, bit)
AttributeError: 'str' object has no attribute '0'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 907, in _resolve_lookup
    current = current[int(bit)]
              ~~~~~~~^^^^^^^^^^
IndexError: string index out of range

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\views.py", line 49, in login_view
    return render(request, 'registration/login.html', {'form': form})
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1075, in render
    output = self.filter_expression.resolve(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 743, in resolve
    arg_vals.append(arg.resolve(context))
                    ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 854, in resolve
    value = self._resolve_lookup(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 914, in _resolve_lookup
    raise VariableDoesNotExist(
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
    )  # missing attribute
    ^
django.template.base.VariableDoesNotExist: Failed lookup for key [0] in ''
ERROR 2025-07-25 13:55:56,751 log 28416 27056 Internal Server Error: /login/
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 891, in _resolve_lookup
    current = current[bit]
              ~~~~~~~^^^^^
TypeError: string indices must be integers, not 'str'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 901, in _resolve_lookup
    current = getattr(current, bit)
AttributeError: 'str' object has no attribute '0'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 907, in _resolve_lookup
    current = current[int(bit)]
              ~~~~~~~^^^^^^^^^^
IndexError: string index out of range

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\views.py", line 49, in login_view
    return render(request, 'registration/login.html', {'form': form})
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1075, in render
    output = self.filter_expression.resolve(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 743, in resolve
    arg_vals.append(arg.resolve(context))
                    ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 854, in resolve
    value = self._resolve_lookup(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 914, in _resolve_lookup
    raise VariableDoesNotExist(
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
    )  # missing attribute
    ^
django.template.base.VariableDoesNotExist: Failed lookup for key [0] in ''
ERROR 2025-07-25 13:55:56,756 basehttp 28416 27056 "GET /login/ HTTP/1.1" 500 185110
INFO 2025-07-25 13:55:58,847 basehttp 28416 27056 "GET /supply-request/ HTTP/1.1" 302 0
ERROR 2025-07-25 13:55:58,852 middleware 28416 27056 Exception for user : VariableDoesNotExist: Failed lookup for key [0] in ''
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 891, in _resolve_lookup
    current = current[bit]
              ~~~~~~~^^^^^
TypeError: string indices must be integers, not 'str'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 901, in _resolve_lookup
    current = getattr(current, bit)
AttributeError: 'str' object has no attribute '0'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 907, in _resolve_lookup
    current = current[int(bit)]
              ~~~~~~~^^^^^^^^^^
IndexError: string index out of range

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\views.py", line 49, in login_view
    return render(request, 'registration/login.html', {'form': form})
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1075, in render
    output = self.filter_expression.resolve(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 743, in resolve
    arg_vals.append(arg.resolve(context))
                    ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 854, in resolve
    value = self._resolve_lookup(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 914, in _resolve_lookup
    raise VariableDoesNotExist(
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
    )  # missing attribute
    ^
django.template.base.VariableDoesNotExist: Failed lookup for key [0] in ''
ERROR 2025-07-25 13:55:58,901 log 28416 27056 Internal Server Error: /login/
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 891, in _resolve_lookup
    current = current[bit]
              ~~~~~~~^^^^^
TypeError: string indices must be integers, not 'str'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 901, in _resolve_lookup
    current = getattr(current, bit)
AttributeError: 'str' object has no attribute '0'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 907, in _resolve_lookup
    current = current[int(bit)]
              ~~~~~~~^^^^^^^^^^
IndexError: string index out of range

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\views.py", line 49, in login_view
    return render(request, 'registration/login.html', {'form': form})
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1075, in render
    output = self.filter_expression.resolve(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 743, in resolve
    arg_vals.append(arg.resolve(context))
                    ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 854, in resolve
    value = self._resolve_lookup(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 914, in _resolve_lookup
    raise VariableDoesNotExist(
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
    )  # missing attribute
    ^
django.template.base.VariableDoesNotExist: Failed lookup for key [0] in ''
ERROR 2025-07-25 13:55:58,908 basehttp 28416 27056 "GET /login/?next=/supply-request/ HTTP/1.1" 500 185504
INFO 2025-07-25 13:56:00,597 basehttp 28416 27056 "GET /supply-request/10/ HTTP/1.1" 302 0
ERROR 2025-07-25 13:56:00,606 middleware 28416 27056 Exception for user : VariableDoesNotExist: Failed lookup for key [0] in ''
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 891, in _resolve_lookup
    current = current[bit]
              ~~~~~~~^^^^^
TypeError: string indices must be integers, not 'str'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 901, in _resolve_lookup
    current = getattr(current, bit)
AttributeError: 'str' object has no attribute '0'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 907, in _resolve_lookup
    current = current[int(bit)]
              ~~~~~~~^^^^^^^^^^
IndexError: string index out of range

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\views.py", line 49, in login_view
    return render(request, 'registration/login.html', {'form': form})
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1075, in render
    output = self.filter_expression.resolve(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 743, in resolve
    arg_vals.append(arg.resolve(context))
                    ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 854, in resolve
    value = self._resolve_lookup(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 914, in _resolve_lookup
    raise VariableDoesNotExist(
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
    )  # missing attribute
    ^
django.template.base.VariableDoesNotExist: Failed lookup for key [0] in ''
ERROR 2025-07-25 13:56:00,656 log 28416 27056 Internal Server Error: /login/
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 891, in _resolve_lookup
    current = current[bit]
              ~~~~~~~^^^^^
TypeError: string indices must be integers, not 'str'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 901, in _resolve_lookup
    current = getattr(current, bit)
AttributeError: 'str' object has no attribute '0'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 907, in _resolve_lookup
    current = current[int(bit)]
              ~~~~~~~^^^^^^^^^^
IndexError: string index out of range

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\dev\smartsupply\suptrack\views.py", line 49, in login_view
    return render(request, 'registration/login.html', {'form': form})
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 1075, in render
    output = self.filter_expression.resolve(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 743, in resolve
    arg_vals.append(arg.resolve(context))
                    ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 854, in resolve
    value = self._resolve_lookup(context)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\template\base.py", line 914, in _resolve_lookup
    raise VariableDoesNotExist(
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
    )  # missing attribute
    ^
django.template.base.VariableDoesNotExist: Failed lookup for key [0] in ''
ERROR 2025-07-25 13:56:00,660 basehttp 28416 27056 "GET /login/?next=/supply-request/10/ HTTP/1.1" 500 185531
ERROR 2025-07-25 13:56:05,227 manage_inventory 13744 24424 Command failed: '<=' not supported between instances of 'int' and 'str'
ERROR 2025-07-25 13:57:09,536 manage_inventory 25584 17096 Command failed: '<=' not supported between instances of 'int' and 'str'
ERROR 2025-07-25 13:57:55,695 manage_inventory 3744 25332 Command failed: 'SupplyItem' object has no attribute 'unit_price'
INFO 2025-07-25 13:58:29,785 manage_inventory 26832 7132 Stock adjustment: IT001 (Laptop Computer) from 25 to 20. Reason: Testing stock update
ERROR 2025-07-25 13:58:51,993 manage_users 13964 21380 Command failed: Failed to create user: NOT NULL constraint failed: user_profiles.phone_number
ERROR 2025-07-25 13:59:59,631 manage_inventory 25304 11956 Command failed: Cannot resolve keyword 'supply_request' into field. Choices are: id, notes, quantity_approved, quantity_requested, request, request_id, stocktransaction, supply_item, supply_item_id, unit_price
INFO 2025-07-25 14:05:29,474 autoreload 27164 7268 Watching for file changes with StatReloader
