from django.test import TestCase
from django.contrib.auth.models import User
from django.utils import timezone
from datetime import timedelta
import uuid
from .models import SupplyRequest, RequestItem, SupplyItem, Category

class SupplyRequestModelTest(TestCase):
    def setUp(self):
        # Create a test user
        self.test_user = User.objects.create_user(
            username='testuser_unittest',
            email='<EMAIL>',
            password='password123'
        )
        
        # Create a test category
        self.test_category = Category.objects.create(
            name='Test Category Unittest',
            description='Test category description for unit test'
        )
        
        # Create a test supply item
        self.test_supply_item = SupplyItem.objects.create(
            item_code=f'ITEM-{uuid.uuid4().hex[:8]}',
            name='Test Item Unittest',
            description='Test item description for unit test',
            category=self.test_category,
            unit_of_measure='Each',
            unit_cost=10.00,
            current_stock=100,
            minimum_threshold=20,
            supplier='Test Supplier'
        )
    
    def test_create_supply_request(self):
        # Create a supply request
        requested_date = timezone.now()
        required_date = requested_date + timedelta(days=7)
        
        # Generate a unique request ID for testing
        test_request_id = f'REQ-TEST-{uuid.uuid4().hex[:8]}'
        
        supply_request = SupplyRequest.objects.create(
            request_id=test_request_id,
            requester=self.test_user,
            department='IT',
            status='pending',  # Use lowercase to match model choices
            justification='Test justification for unit test',
            requested_date=requested_date,
            required_date=required_date,
            notes='Test notes for unit test'
        )
        
        # Create a request item
        request_item = RequestItem.objects.create(
            request=supply_request,
            supply_item=self.test_supply_item,
            quantity_requested=5,
            unit_price=self.test_supply_item.unit_cost,
            notes='Test item notes for unit test'
        )
        
        # Test properties
        self.assertEqual(supply_request.total_items, 1)
        self.assertEqual(float(supply_request.total_cost), 50.00)  # Convert Decimal to float for comparison