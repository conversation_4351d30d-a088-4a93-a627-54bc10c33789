{% extends 'base.html' %}

{% block title %}Edit Item: {{ item.name }}{% endblock %}

{% block content %}
<div class="bg-white p-8 rounded-lg shadow-lg">
    <h1 class="text-3xl font-bold text-gray-800 mb-6">Edit: {{ item.name }}</h1>

    <form method="post">
        {% csrf_token %}
        <div class="space-y-4">
            {{ form.as_p }}
        </div>
        <div class="mt-8 flex justify-end space-x-4">
            <a href="{% url 'suptrack:item_detail' item.pk %}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300">Cancel</a>
            <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300">Save Changes</button>
        </div>
    </form>
</div>
{% endblock %}
