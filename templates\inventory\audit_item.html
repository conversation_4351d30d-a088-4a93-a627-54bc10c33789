{% extends 'base.html' %}

{% block content %}
<div class="max-w-4xl mx-auto py-10 px-4 sm:px-6 lg:px-8">
    <h1 class="text-3xl font-bold text-gray-900 mb-6">Inventory Audit</h1>

    <div class="bg-white shadow overflow-hidden sm:rounded-lg mb-8">
        <div class="px-4 py-5 sm:px-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900">{{ item.name }}</h3>
            <p class="mt-1 max-w-2xl text-sm text-gray-500">Item Code: {{ item.item_code }}</p>
        </div>
        <div class="border-t border-gray-200 px-4 py-5 sm:p-0">
            <dl class="sm:divide-y sm:divide-gray-200">
                <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Current Stock</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ item.current_stock }} {{ item.get_unit_of_measure_display }}</dd>
                </div>
            </dl>
        </div>
    </div>

    <form action="{% url 'suptrack:process_audit' item.pk %}" method="post" class="bg-white p-6 rounded-lg shadow-md">
        {% csrf_token %}
        <h3 class="text-xl font-bold text-gray-900 mb-4">Verify Stock Level</h3>
        <div class="mb-4">
            <label for="id_audited_quantity" class="block text-sm font-medium text-gray-700">Audited Quantity</label>
            <input type="number" name="audited_quantity" id="id_audited_quantity" min="0" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" required>
        </div>
        <div class="mb-4">
            <label for="id_audit_notes" class="block text-sm font-medium text-gray-700">Notes</label>
            <textarea name="notes" id="id_audit_notes" rows="3" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"></textarea>
        </div>
        <button type="submit" class="w-full bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700">Submit Audit</button>
    </form>

</div>
{% endblock %}
