{% extends 'base.html' %}

{% block title %}New Supply Request{% endblock %}

{% block header_title %}Create Supply Request{% endblock %}

{% block breadcrumb_items %}
<li>
    <div class="flex items-center">
        <svg class="flex-shrink-0 h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
        </svg>
        <a href="{% url 'suptrack:supply_request_list' %}" class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700">Supply Requests</a>
    </div>
</li>
<li>
    <div class="flex items-center">
        <svg class="flex-shrink-0 h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
        </svg>
        <span class="ml-4 text-sm font-medium text-gray-500">Create Request</span>
    </div>
</li>
{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="mb-8">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Create Supply Request</h1>
            <p class="mt-2 text-sm text-gray-600">Submit a new request for supplies and materials needed by your department.</p>
        </div>
        <div class="mt-4 sm:mt-0">
            <a href="{% url 'suptrack:supply_request_list' %}"
               class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Requests
            </a>
        </div>
    </div>
</div>

<!-- Progress Indicator -->
<div class="mb-8">
    <nav aria-label="Progress">
        <ol class="flex items-center">
            <li class="relative pr-8 sm:pr-20">
                <div class="absolute inset-0 flex items-center" aria-hidden="true">
                    <div class="h-0.5 w-full bg-blue-600"></div>
                </div>
                <div class="relative w-8 h-8 flex items-center justify-center bg-blue-600 rounded-full">
                    <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <span class="absolute top-10 left-0 text-xs font-medium text-blue-600">Request Details</span>
            </li>
            <li class="relative pr-8 sm:pr-20">
                <div class="absolute inset-0 flex items-center" aria-hidden="true">
                    <div class="h-0.5 w-full bg-gray-200"></div>
                </div>
                <div class="relative w-8 h-8 flex items-center justify-center bg-white border-2 border-blue-600 rounded-full">
                    <span class="h-2.5 w-2.5 bg-blue-600 rounded-full"></span>
                </div>
                <span class="absolute top-10 left-0 text-xs font-medium text-blue-600">Add Items</span>
            </li>
            <li class="relative">
                <div class="relative w-8 h-8 flex items-center justify-center bg-white border-2 border-gray-300 rounded-full">
                    <span class="h-2.5 w-2.5 bg-transparent rounded-full border-2 border-gray-300"></span>
                </div>
                <span class="absolute top-10 left-0 text-xs font-medium text-gray-500">Review & Submit</span>
            </li>
        </ol>
    </nav>
</div>

<!-- Main Form -->
<div class="bg-white shadow-lg rounded-lg overflow-hidden" x-data="requestForm()">
    <form method="post" x-ref="form" @submit="handleSubmit">
        {% csrf_token %}

        <!-- Request Details Section -->
        <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <h3 class="text-lg font-semibold text-gray-900">Request Details</h3>
            <p class="mt-1 text-sm text-gray-600">Provide basic information about your supply request.</p>
        </div>

        <div class="px-6 py-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Department Field -->
                <div>
                    <label for="{{ form.department.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Department <span class="text-red-500">*</span>
                    </label>
                    <div class="mt-1">
                        {{ form.department }}
                        {% if form.department.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {{ form.department.errors.0 }}
                            </div>
                        {% endif %}
                    </div>
                    <p class="mt-2 text-sm text-gray-500">Select the department making this request.</p>
                </div>

                <!-- Required Date Field -->
                <div>
                    <label for="{{ form.required_date.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Required Date <span class="text-red-500">*</span>
                    </label>
                    <div class="mt-1">
                        {{ form.required_date }}
                        {% if form.required_date.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {{ form.required_date.errors.0 }}
                            </div>
                        {% endif %}
                    </div>
                    <p class="mt-2 text-sm text-gray-500">When do you need these supplies?</p>
                </div>
            </div>

            <!-- Justification Field -->
            <div class="mt-6">
                <label for="{{ form.justification.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    Justification <span class="text-red-500">*</span>
                </label>
                <div class="mt-1">
                    {{ form.justification }}
                    {% if form.justification.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {{ form.justification.errors.0 }}
                        </div>
                    {% endif %}
                </div>
                <p class="mt-2 text-sm text-gray-500">Explain why these supplies are needed and how they will be used.</p>
            </div>
        </div>

        <!-- Request Items Section -->
        <div class="border-t border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900">Request Items</h3>
                        <p class="mt-1 text-sm text-gray-600">Add the supplies and materials you need.</p>
                    </div>
                    <button type="button" @click="addForm"
                            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150 ease-in-out">
                        <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Add Item
                    </button>
                </div>
            </div>

            <div class="px-6 py-6">
                {{ formset.management_form }}

                <div id="formset-container" class="space-y-4">
                    <!-- Dynamic forms will be inserted here -->
                </div>

                <div x-show="!hasItems" class="text-center py-8 border-2 border-dashed border-gray-300 rounded-lg">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No items added</h3>
                    <p class="mt-1 text-sm text-gray-500">Get started by adding your first item to the request.</p>
                    <div class="mt-6">
                        <button type="button" @click="addForm"
                                class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            Add First Item
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Summary Section -->
        <div class="border-t border-gray-200 bg-gray-50 px-6 py-4" x-show="hasItems">
            <div class="flex items-center justify-between">
                <div>
                    <h4 class="text-sm font-medium text-gray-900">Request Summary</h4>
                    <p class="text-sm text-gray-500" x-text="`${itemCount} item${itemCount !== 1 ? 's' : ''} • Estimated total: $${estimatedTotal.toFixed(2)}`"></p>
                </div>
                <div class="flex items-center space-x-3">
                    <button type="button" @click="resetForm"
                            class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <svg class="-ml-0.5 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        Reset
                    </button>
                    <button type="submit"
                            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150 ease-in-out">
                        <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                        </svg>
                        Submit Request
                    </button>
                </div>
            </div>
        </div>

        <!-- Submit Section for when no items -->
        <div class="border-t border-gray-200 bg-gray-50 px-6 py-4" x-show="!hasItems">
            <div class="flex justify-end">
                <button type="submit" disabled
                        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-gray-400 cursor-not-allowed">
                    <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                    </svg>
                    Submit Request
                </button>
            </div>
            <p class="mt-2 text-sm text-gray-500 text-right">Add at least one item to submit your request.</p>
        </div>
    </form>
</div>

<!-- Item Form Template -->
<template id="form-template">
    <div class="item-form bg-white border border-gray-200 rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow duration-200">
        <div class="flex items-start justify-between mb-4">
            <h4 class="text-lg font-medium text-gray-900">Item <span class="item-number"></span></h4>
            <button type="button" @click="removeForm($el)"
                    class="inline-flex items-center p-2 border border-transparent rounded-full text-red-400 hover:text-red-600 hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition duration-150 ease-in-out">
                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
                <span class="sr-only">Remove item</span>
            </button>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <label class="block text-sm font-medium text-gray-700">Supply Item <span class="text-red-500">*</span></label>
                <div class="mt-1">
                    {{ formset.empty_form.supply_item }}
                </div>
                <p class="mt-2 text-sm text-gray-500">Select the item you need from the available supplies.</p>
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700">Quantity <span class="text-red-500">*</span></label>
                <div class="mt-1">
                    {{ formset.empty_form.quantity_requested }}
                </div>
                <p class="mt-2 text-sm text-gray-500">How many units do you need?</p>
            </div>
        </div>

        <div class="mt-4 p-3 bg-blue-50 rounded-md">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-blue-700">
                        <strong>Tip:</strong> Make sure to check current stock levels and select appropriate quantities to avoid delays in approval.
                    </p>
                </div>
            </div>
        </div>
    </div>
</template>

{% endblock %}

{% block extra_js %}
<script>
function requestForm() {
    return {
        totalFormsInput: null,
        formContainer: null,
        template: null,
        formCount: 0,
        hasItems: false,
        itemCount: 0,
        estimatedTotal: 0,

        init() {
            this.totalFormsInput = document.querySelector('#id_request_items-TOTAL_FORMS');
            this.formContainer = document.querySelector('#formset-container');
            this.template = document.querySelector('#form-template');

            if (this.totalFormsInput) {
                this.formCount = parseInt(this.totalFormsInput.value) || 0;
            }

            this.updateItemCount();
            this.updateFormNumbers();

            // Add form validation
            this.setupFormValidation();
        },

        addForm() {
            if (!this.template || !this.formContainer) return;

            const newForm = this.template.content.cloneNode(true);
            const formHtml = newForm.firstElementChild.outerHTML.replace(/__prefix__/g, this.formCount);
            this.formContainer.insertAdjacentHTML('beforeend', formHtml);
            this.formCount++;
            this.updateTotalForms();
            this.updateItemCount();
            this.updateFormNumbers();

            // Add animation
            const newFormElement = this.formContainer.lastElementChild;
            newFormElement.style.opacity = '0';
            newFormElement.style.transform = 'translateY(20px)';
            setTimeout(() => {
                newFormElement.style.transition = 'opacity 0.3s ease-out, transform 0.3s ease-out';
                newFormElement.style.opacity = '1';
                newFormElement.style.transform = 'translateY(0)';
            }, 10);
        },

        removeForm(element) {
            const formElement = element.closest('.item-form');
            if (formElement) {
                // Add exit animation
                formElement.style.transition = 'opacity 0.3s ease-out, transform 0.3s ease-out';
                formElement.style.opacity = '0';
                formElement.style.transform = 'translateY(-20px)';

                setTimeout(() => {
                    formElement.remove();
                    this.updateTotalForms();
                    this.updateItemCount();
                    this.updateFormNumbers();
                }, 300);
            }
        },

        updateTotalForms() {
            if (this.totalFormsInput && this.formContainer) {
                this.totalFormsInput.value = this.formContainer.children.length;
            }
        },

        updateItemCount() {
            if (this.formContainer) {
                this.itemCount = this.formContainer.children.length;
                this.hasItems = this.itemCount > 0;
                this.calculateEstimatedTotal();
            }
        },

        updateFormNumbers() {
            if (this.formContainer) {
                const forms = this.formContainer.querySelectorAll('.item-form');
                forms.forEach((form, index) => {
                    const numberSpan = form.querySelector('.item-number');
                    if (numberSpan) {
                        numberSpan.textContent = index + 1;
                    }
                });
            }
        },

        calculateEstimatedTotal() {
            // This would need to be implemented with actual price data
            // For now, we'll use a placeholder calculation
            this.estimatedTotal = this.itemCount * 25; // Placeholder: $25 per item
        },

        resetForm() {
            if (confirm('Are you sure you want to reset the form? All entered data will be lost.')) {
                if (this.formContainer) {
                    this.formContainer.innerHTML = '';
                    this.formCount = 0;
                    this.updateTotalForms();
                    this.updateItemCount();
                }

                // Reset main form fields
                const form = this.$refs.form;
                if (form) {
                    form.reset();
                }
            }
        },

        handleSubmit(event) {
            if (!this.hasItems) {
                event.preventDefault();
                alert('Please add at least one item to your request.');
                return false;
            }

            // Add loading state
            const submitButton = event.target.querySelector('button[type="submit"]');
            if (submitButton) {
                submitButton.disabled = true;
                submitButton.innerHTML = `
                    <svg class="animate-spin -ml-1 mr-2 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Submitting...
                `;
            }

            return true;
        },

        setupFormValidation() {
            // Add real-time validation feedback
            const form = this.$refs.form;
            if (form) {
                const inputs = form.querySelectorAll('input, select, textarea');
                inputs.forEach(input => {
                    input.addEventListener('blur', () => {
                        this.validateField(input);
                    });
                });
            }
        },

        validateField(field) {
            const errorElement = field.parentElement.querySelector('.error-message');
            if (errorElement) {
                errorElement.remove();
            }

            if (field.hasAttribute('required') && !field.value.trim()) {
                this.showFieldError(field, 'This field is required.');
            }
        },

        showFieldError(field, message) {
            const errorElement = document.createElement('div');
            errorElement.className = 'error-message mt-1 text-sm text-red-600';
            errorElement.textContent = message;
            field.parentElement.appendChild(errorElement);
        }
    }
}
</script>
{% endblock %}
