<table class="min-w-full leading-normal">
    <thead>
        <tr>
            <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Item Name</th>
            <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Category</th>
            <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">On Hand</th>
            <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Reorder Level</th>
            <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100"></th>
        </tr>
    </thead>
    <tbody>
        {% for item in items %}
        <tr class="{% if item.quantity_on_hand <= item.reorder_level %}bg-red-100{% endif %}">
            <td class="px-5 py-5 border-b border-gray-200 bg-transparent text-sm">{{ item.name }}</td>
            <td class="px-5 py-5 border-b border-gray-200 bg-transparent text-sm">{{ item.category.name }}</td>
            <td class="px-5 py-5 border-b border-gray-200 bg-transparent text-sm">{{ item.quantity_on_hand }} {{ item.unit_of_measure }}</td>
            <td class="px-5 py-5 border-b border-gray-200 bg-transparent text-sm">{{ item.reorder_level }}</td>
            <td class="px-5 py-5 border-b border-gray-200 bg-transparent text-sm text-right">
                <a href="{% url 'suptrack:item_detail' item.pk %}" class="text-indigo-600 hover:text-indigo-900">Details</a>
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>
