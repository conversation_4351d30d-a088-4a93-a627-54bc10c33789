import os
import sys
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smartsupply.settings')
django.setup()

from django.contrib.auth.models import User
from django.utils import timezone
from datetime import timed<PERSON>ta
import uuid
from suptrack.models import SupplyRequest, RequestItem, SupplyItem, Category

def verify_request_model():
    print("\n=== Verifying SupplyRequest Model ===")
    
    # Check if the models exist
    print("Checking model existence...")
    print(f"SupplyRequest model exists: {SupplyRequest.__name__}")
    print(f"RequestItem model exists: {RequestItem.__name__}")
    
    # Check model fields
    print("\nChecking SupplyRequest fields...")
    supply_request_fields = [f.name for f in SupplyRequest._meta.get_fields()]
    print(f"SupplyRequest fields: {', '.join(supply_request_fields)}")
    
    print("\nChecking RequestItem fields...")
    request_item_fields = [f.name for f in RequestItem._meta.get_fields()]
    print(f"RequestItem fields: {', '.join(request_item_fields)}")
    
    # Check existing data
    print("\nChecking existing data...")
    supply_requests = SupplyRequest.objects.all()
    print(f"Number of supply requests: {supply_requests.count()}")
    
    request_items = RequestItem.objects.all()
    print(f"Number of request items: {request_items.count()}")
    
    # Display a sample supply request if available
    if supply_requests.exists():
        sample_request = supply_requests.first()
        print(f"\nSample SupplyRequest:")
        print(f"  ID: {sample_request.id}")
        print(f"  Request ID: {sample_request.request_id}")
        print(f"  Requester: {sample_request.requester.username}")
        print(f"  Department: {sample_request.department}")
        print(f"  Status: {sample_request.status}")
        print(f"  Requested Date: {sample_request.requested_date}")
        print(f"  Required Date: {sample_request.required_date}")
        print(f"  Total Items: {sample_request.total_items}")
        print(f"  Total Cost: {sample_request.total_cost}")
        
        # Display associated request items
        sample_items = sample_request.request_items.all()
        if sample_items.exists():
            print(f"\n  Associated RequestItems:")
            for item in sample_items:
                print(f"    - {item.supply_item.name}: {item.quantity_requested} x ${item.unit_price} = ${item.total_cost}")
    
    print("\nVerification complete!")

if __name__ == '__main__':
    verify_request_model()