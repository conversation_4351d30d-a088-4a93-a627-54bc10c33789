/**
 * Enhanced Dashboard JavaScript functionality
 * Handles sidebar toggling, responsive behavior, animations, and interactive features
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize dashboard components
    initializeSidebar();
    initializeAnimations();
    initializeInteractiveElements();
    initializeResponsiveFeatures();

    // Initialize sidebar state based on screen size
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('main-content');
    const sidebarToggle = document.getElementById('sidebar-toggle');
    const closeSidebarBtn = document.getElementById('close-sidebar');
    
    // Function to check if we're on mobile view
    function isMobileView() {
        return window.innerWidth < 768;
    }
    
    // Function to update sidebar state
    function updateSidebarState(isOpen) {
        if (isOpen) {
            sidebar.classList.remove('hidden');
            sidebar.classList.remove('transform');
            sidebar.classList.remove('-translate-x-full');
            document.body.classList.add('sidebar-open');
            document.body.classList.remove('sidebar-closed');
            
            // On mobile, add overlay
            if (isMobileView()) {
                const overlay = document.createElement('div');
                overlay.id = 'sidebar-overlay';
                overlay.className = 'fixed inset-0 bg-black bg-opacity-50 z-10';
                overlay.addEventListener('click', () => updateSidebarState(false));
                document.body.appendChild(overlay);
            }
        } else {
            if (isMobileView()) {
                sidebar.classList.add('transform');
                sidebar.classList.add('-translate-x-full');
                
                // Remove overlay if it exists
                const overlay = document.getElementById('sidebar-overlay');
                if (overlay) {
                    overlay.remove();
                }
            } else {
                sidebar.classList.add('hidden');
            }
            
            document.body.classList.remove('sidebar-open');
            document.body.classList.add('sidebar-closed');
        }
    }
    
    // Initialize sidebar state based on screen size
    if (isMobileView()) {
        updateSidebarState(false);
    } else {
        updateSidebarState(true);
    }
    
    // Toggle sidebar when the toggle button is clicked
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', function() {
            const isCurrentlyOpen = !sidebar.classList.contains('-translate-x-full') && 
                                   !sidebar.classList.contains('hidden');
            updateSidebarState(!isCurrentlyOpen);
        });
    }
    
    // Close sidebar when close button is clicked
    if (closeSidebarBtn) {
        closeSidebarBtn.addEventListener('click', function() {
            updateSidebarState(false);
        });
    }
    
    // Update sidebar state when window is resized
    window.addEventListener('resize', function() {
        if (isMobileView()) {
            // On mobile, sidebar should be hidden by default
            if (!document.body.classList.contains('sidebar-closed')) {
                updateSidebarState(false);
            }
        } else {
            // On desktop, sidebar should be visible by default
            if (sidebar.classList.contains('-translate-x-full') || 
                sidebar.classList.contains('hidden')) {
                updateSidebarState(true);
            }
        }
    });
    
    // Handle submenu toggles
    const submenuToggles = document.querySelectorAll('.submenu-toggle');
    submenuToggles.forEach(toggle => {
        toggle.addEventListener('click', function(e) {
            e.preventDefault();
            const submenu = this.nextElementSibling;
            const icon = this.querySelector('svg');

            if (submenu.classList.contains('hidden')) {
                submenu.classList.remove('hidden');
                icon.classList.add('rotate-180');
            } else {
                submenu.classList.add('hidden');
                icon.classList.remove('rotate-180');
            }
        });
    });
});

/**
 * Initialize sidebar functionality
 */
function initializeSidebar() {
    // Add active state detection
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('nav a[href]');

    navLinks.forEach(link => {
        if (link.getAttribute('href') === currentPath) {
            link.classList.add('bg-blue-900', 'text-white');
            link.classList.remove('text-blue-100', 'hover:bg-blue-700');
        }
    });

    // Enhanced sidebar animations
    const sidebar = document.getElementById('sidebar');
    if (sidebar) {
        sidebar.style.transition = 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
    }
}

/**
 * Initialize animations and transitions
 */
function initializeAnimations() {
    // Stagger animation for dashboard cards
    const cards = document.querySelectorAll('.dashboard-card, .bg-white.shadow-lg');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'opacity 0.6s ease-out, transform 0.6s ease-out';

        setTimeout(() => {
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });

    // Add loading states for buttons
    const buttons = document.querySelectorAll('button, .btn, a[href*="create"]');
    buttons.forEach(button => {
        button.addEventListener('click', function() {
            if (!this.classList.contains('loading')) {
                this.classList.add('loading');
                const originalText = this.textContent;
                this.innerHTML = '<span class="loading-spinner"></span> Loading...';

                // Reset after 2 seconds if no navigation occurs
                setTimeout(() => {
                    if (this.classList.contains('loading')) {
                        this.classList.remove('loading');
                        this.textContent = originalText;
                    }
                }, 2000);
            }
        });
    });
}

/**
 * Initialize interactive elements
 */
function initializeInteractiveElements() {
    // Enhanced table row interactions
    const tableRows = document.querySelectorAll('tbody tr');
    tableRows.forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.01)';
            this.style.transition = 'transform 0.2s ease-out';
        });

        row.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
        });
    });

    // Add ripple effect to buttons
    const rippleButtons = document.querySelectorAll('.btn, button, .dashboard-button');
    rippleButtons.forEach(button => {
        button.addEventListener('click', createRippleEffect);
    });

    // Initialize tooltips
    initializeTooltips();

    // Add keyboard navigation
    initializeKeyboardNavigation();
}

/**
 * Initialize responsive features
 */
function initializeResponsiveFeatures() {
    // Responsive table handling
    const tables = document.querySelectorAll('table');
    tables.forEach(table => {
        const wrapper = table.closest('.overflow-x-auto');
        if (wrapper) {
            // Add scroll indicators
            addScrollIndicators(wrapper);
        }
    });

    // Mobile-specific enhancements
    if (window.innerWidth < 768) {
        initializeMobileFeatures();
    }

    // Handle orientation changes
    window.addEventListener('orientationchange', function() {
        setTimeout(() => {
            handleOrientationChange();
        }, 100);
    });
}

/**
 * Create ripple effect for buttons
 */
function createRippleEffect(e) {
    const button = e.currentTarget;
    const ripple = document.createElement('span');
    const rect = button.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = e.clientX - rect.left - size / 2;
    const y = e.clientY - rect.top - size / 2;

    ripple.style.width = ripple.style.height = size + 'px';
    ripple.style.left = x + 'px';
    ripple.style.top = y + 'px';
    ripple.classList.add('ripple');

    button.appendChild(ripple);

    setTimeout(() => {
        ripple.remove();
    }, 600);
}

/**
 * Initialize tooltips
 */
function initializeTooltips() {
    const tooltipElements = document.querySelectorAll('[data-tooltip]');
    tooltipElements.forEach(element => {
        element.addEventListener('mouseenter', showTooltip);
        element.addEventListener('mouseleave', hideTooltip);
    });
}

/**
 * Initialize keyboard navigation
 */
function initializeKeyboardNavigation() {
    document.addEventListener('keydown', function(e) {
        // ESC key closes sidebar on mobile
        if (e.key === 'Escape') {
            const sidebar = document.getElementById('sidebar');
            if (sidebar && window.innerWidth < 1024) {
                const sidebarOpen = !sidebar.classList.contains('-translate-x-full');
                if (sidebarOpen) {
                    updateSidebarState(false);
                }
            }
        }

        // Ctrl/Cmd + K for quick search (if implemented)
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            // Focus search input if available
            const searchInput = document.querySelector('input[type="search"]');
            if (searchInput) {
                searchInput.focus();
            }
        }
    });
}

/**
 * Add scroll indicators to tables
 */
function addScrollIndicators(wrapper) {
    const leftIndicator = document.createElement('div');
    const rightIndicator = document.createElement('div');

    leftIndicator.className = 'scroll-indicator left';
    rightIndicator.className = 'scroll-indicator right';

    wrapper.appendChild(leftIndicator);
    wrapper.appendChild(rightIndicator);

    wrapper.addEventListener('scroll', function() {
        const scrollLeft = this.scrollLeft;
        const scrollWidth = this.scrollWidth;
        const clientWidth = this.clientWidth;

        leftIndicator.style.opacity = scrollLeft > 0 ? '1' : '0';
        rightIndicator.style.opacity = scrollLeft < scrollWidth - clientWidth ? '1' : '0';
    });
}

/**
 * Initialize mobile-specific features
 */
function initializeMobileFeatures() {
    // Add touch gestures for sidebar
    let touchStartX = 0;
    let touchEndX = 0;

    document.addEventListener('touchstart', function(e) {
        touchStartX = e.changedTouches[0].screenX;
    });

    document.addEventListener('touchend', function(e) {
        touchEndX = e.changedTouches[0].screenX;
        handleSwipeGesture();
    });

    function handleSwipeGesture() {
        const swipeThreshold = 50;
        const diff = touchStartX - touchEndX;

        if (Math.abs(diff) > swipeThreshold) {
            const sidebar = document.getElementById('sidebar');
            if (sidebar) {
                if (diff > 0) {
                    // Swipe left - close sidebar
                    updateSidebarState(false);
                } else {
                    // Swipe right - open sidebar
                    updateSidebarState(true);
                }
            }
        }
    }
}

/**
 * Handle orientation changes
 */
function handleOrientationChange() {
    // Recalculate layouts and update responsive elements
    const cards = document.querySelectorAll('.dashboard-card');
    cards.forEach(card => {
        card.style.transition = 'none';
        setTimeout(() => {
            card.style.transition = '';
        }, 100);
    });
}

/**
 * Show tooltip
 */
function showTooltip(e) {
    const tooltip = document.createElement('div');
    tooltip.className = 'tooltip';
    tooltip.textContent = e.target.getAttribute('data-tooltip');
    document.body.appendChild(tooltip);

    const rect = e.target.getBoundingClientRect();
    tooltip.style.left = rect.left + rect.width / 2 - tooltip.offsetWidth / 2 + 'px';
    tooltip.style.top = rect.top - tooltip.offsetHeight - 8 + 'px';
}

/**
 * Hide tooltip
 */
function hideTooltip() {
    const tooltip = document.querySelector('.tooltip');
    if (tooltip) {
        tooltip.remove();
    }
}