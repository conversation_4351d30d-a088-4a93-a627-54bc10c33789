/* Dashboard specific styles */

/* Mobile-first styles */
.dashboard-container {
    padding: 1rem;
}

/* Enhanced card animations and hover effects */
.dashboard-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateZ(0); /* Enable hardware acceleration */
}

.dashboard-card:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Smooth transitions for interactive elements */
.dashboard-button {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.dashboard-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.dashboard-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Enhanced table styling */
.dashboard-table-container {
    overflow-x: auto;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.dashboard-table-row {
    transition: background-color 0.15s ease-in-out;
}

.dashboard-table-row:hover {
    background-color: rgba(249, 250, 251, 0.8);
}

/* Status badge animations */
.status-badge {
    transition: all 0.2s ease-in-out;
    animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading states */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Enhanced responsive design */

/* Mobile devices (320px - 768px) */
@media (max-width: 768px) {
    .dashboard-container {
        padding: 0.75rem;
    }

    /* Mobile-optimized cards */
    .dashboard-card {
        margin-bottom: 1rem;
    }

    .dashboard-card:hover {
        transform: translateY(-2px) scale(1.01);
    }

    /* Mobile table improvements */
    .dashboard-table-container {
        border-radius: 0.375rem;
        margin: 0 -0.75rem;
    }

    /* Compact mobile layout */
    .mobile-compact {
        padding: 0.5rem;
        font-size: 0.875rem;
    }

    /* Touch-friendly buttons */
    .mobile-button {
        min-height: 44px;
        padding: 0.75rem 1rem;
    }

    /* Sidebar adjustments */
    .sidebar-open .main-content {
        margin-left: 0;
    }

    .sidebar-closed .sidebar {
        transform: translateX(-100%);
    }

    .sidebar-toggle {
        display: block;
    }

    /* Mobile navigation improvements */
    .mobile-nav-item {
        padding: 0.75rem 1rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
}

/* Small tablets (768px - 1024px) */
@media (min-width: 768px) and (max-width: 1024px) {
    .dashboard-container {
        padding: 1.25rem;
    }

    /* Tablet-optimized grid */
    .tablet-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.25rem;
    }

    /* Tablet card sizing */
    .dashboard-card {
        padding: 1.25rem;
    }
}

/* Desktop screens (1024px+) */
@media (min-width: 1024px) {
    .dashboard-container {
        padding: 2rem;
    }

    .sidebar-toggle {
        display: none;
    }

    /* Desktop-optimized layouts */
    .desktop-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 1.5rem;
    }

    /* Enhanced hover effects for desktop */
    .desktop-hover:hover {
        transform: translateY(-6px) scale(1.03);
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    }
}

/* Large desktop screens (1440px+) */
@media (min-width: 1440px) {
    .dashboard-container {
        padding: 2.5rem;
        max-width: 1400px;
        margin: 0 auto;
    }

    /* Large screen optimizations */
    .large-desktop-grid {
        grid-template-columns: repeat(5, 1fr);
        gap: 2rem;
    }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .dashboard-card {
        border: 0.5px solid rgba(0, 0, 0, 0.05);
    }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
    .dashboard-card,
    .dashboard-button,
    .status-badge,
    .dashboard-table-row {
        transition: none;
        animation: none;
    }

    .dashboard-card:hover {
        transform: none;
    }
}

/* Enhanced dark mode support */
@media (prefers-color-scheme: dark) {
    .dashboard-card {
        background-color: #1f2937;
        color: #f9fafb;
        border: 1px solid #374151;
    }

    .dashboard-card:hover {
        background-color: #111827;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.1);
    }

    .dashboard-card-header {
        background-color: #111827;
        border-bottom: 1px solid #374151;
    }

    .dashboard-table-container {
        background-color: #1f2937;
        border: 1px solid #374151;
    }

    .dashboard-table-row:hover {
        background-color: rgba(55, 65, 81, 0.3);
    }

    .status-badge {
        border: 1px solid rgba(255, 255, 255, 0.1);
    }
}

/* Focus states for accessibility */
.focus-visible:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
    border-radius: 0.375rem;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .dashboard-card {
        border: 2px solid;
    }

    .dashboard-button {
        border: 2px solid;
    }

    .status-badge {
        border: 2px solid;
        font-weight: 600;
    }
}

/* Print styles */
@media print {
    .dashboard-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #000;
    }

    .dashboard-button,
    .sidebar,
    .mobile-nav {
        display: none;
    }

    .dashboard-table-container {
        overflow: visible;
    }
}

/* Custom scrollbar styling */
.dashboard-table-container::-webkit-scrollbar {
    height: 8px;
}

.dashboard-table-container::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
}

.dashboard-table-container::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
}

.dashboard-table-container::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* Firefox scrollbar */
.dashboard-table-container {
    scrollbar-width: thin;
    scrollbar-color: #cbd5e1 #f1f5f9;
}

/* Breadcrumb enhancements */
.breadcrumb-nav {
    margin-bottom: 1.5rem;
}

.breadcrumb-nav ol {
    display: flex;
    align-items: center;
    space-x: 1rem;
}

.breadcrumb-nav a {
    transition: color 0.15s ease-in-out;
}

.breadcrumb-nav a:hover {
    color: #3b82f6;
}

.breadcrumb-separator {
    color: #9ca3af;
    transition: color 0.15s ease-in-out;
}

/* Page header consistency */
.page-header {
    margin-bottom: 2rem;
}

.page-header h1 {
    font-size: 1.875rem;
    font-weight: 700;
    color: #111827;
    line-height: 2.25rem;
}

.page-header p {
    margin-top: 0.5rem;
    color: #6b7280;
    font-size: 0.875rem;
    line-height: 1.25rem;
}

/* Form consistency */
.form-section {
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    overflow: hidden;
}

.form-section-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
    background-color: #f9fafb;
}

.form-section-content {
    padding: 1.5rem;
}

.form-field {
    margin-bottom: 1.5rem;
}

.form-field label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.25rem;
}

.form-field input,
.form-field select,
.form-field textarea {
    display: block;
    width: 100%;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    font-size: 0.875rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-field input:focus,
.form-field select:focus,
.form-field textarea:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-field .error-message {
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #dc2626;
}

.form-field .help-text {
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #6b7280;
}

/* Status badges consistency */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    line-height: 1;
}

.status-badge.status-pending {
    background-color: #fef3c7;
    color: #92400e;
}

.status-badge.status-approved {
    background-color: #d1fae5;
    color: #065f46;
}

.status-badge.status-rejected {
    background-color: #fee2e2;
    color: #991b1b;
}

.status-badge.status-completed {
    background-color: #dbeafe;
    color: #1e40af;
}

.status-badge.status-released {
    background-color: #e0e7ff;
    color: #5b21b6;
}

/* Ripple effect */
.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* Tooltip styles */
.tooltip {
    position: absolute;
    background: #1f2937;
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    z-index: 1000;
    opacity: 0;
    animation: tooltip-fade-in 0.2s ease-out forwards;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: #1f2937 transparent transparent transparent;
}

@keyframes tooltip-fade-in {
    from {
        opacity: 0;
        transform: translateY(-5px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Scroll indicators */
.scroll-indicator {
    position: absolute;
    top: 0;
    bottom: 0;
    width: 20px;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 10;
}

.scroll-indicator.left {
    left: 0;
    background: linear-gradient(to right, rgba(255, 255, 255, 0.8), transparent);
}

.scroll-indicator.right {
    right: 0;
    background: linear-gradient(to left, rgba(255, 255, 255, 0.8), transparent);
}

/* Loading spinner */
.loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Enhanced button states */
button:disabled,
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* Smooth page transitions */
.page-transition {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.3s ease-out, transform 0.3s ease-out;
}

.page-transition.loaded {
    opacity: 1;
    transform: translateY(0);
}