import os
import sys
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smartsupply.settings')
django.setup()

from django.contrib.auth.models import User
from django.utils import timezone
from datetime import timedelta
from suptrack.models import SupplyRequest, RequestItem, SupplyItem, Category

def test_supply_request_model():
    print("\n=== Testing SupplyRequest Model ===")
    
    # Import uuid for generating unique IDs
    import uuid
    
    # Create test data
    print("Creating test user...")
    # Check if user already exists
    test_username = 'testuser_request_model'
    try:
        test_user = User.objects.get(username=test_username)
        print(f"Using existing user: {test_username}")
    except User.DoesNotExist:
        test_user = User.objects.create_user(
            username=test_username,
            email='<EMAIL>',
            password='password123'
        )
        print(f"Created new user: {test_username}")
    
    print("Creating test category...")
    test_category_name = 'Test Category Request Model'
    try:
        test_category = Category.objects.get(name=test_category_name)
        print(f"Using existing category: {test_category_name}")
    except Category.DoesNotExist:
        test_category = Category.objects.create(
            name=test_category_name,
            description='Test category for request model test'
        )
        print(f"Created new category: {test_category_name}")
    
    print("Creating test supply item...")
    test_item_name = 'Test Item Request Model'
    try:
        test_supply_item = SupplyItem.objects.get(name=test_item_name)
        print(f"Using existing supply item: {test_item_name}")
    except SupplyItem.DoesNotExist:
        test_supply_item = SupplyItem.objects.create(
            item_code=f'ITEM-{uuid.uuid4().hex[:8]}',
            name=test_item_name,
            description='Test item for request model test',
            category=test_category,
            unit_of_measure='Each',
            unit_cost=10.00,
            current_stock=100,
            minimum_threshold=20,
            supplier='Test Supplier'
        )
        print(f"Created new supply item: {test_item_name}")
    
    # Test creating a supply request
    print("Creating test supply request...")
    requested_date = timezone.now()
    required_date = requested_date + timedelta(days=7)
    
    # Generate a unique request ID with timestamp
    import uuid
    test_request_id = f'REQ-TEST-{uuid.uuid4().hex[:8]}'
    
    # Check if a test request already exists and delete it if it does
    try:
        old_test_requests = SupplyRequest.objects.filter(request_id__startswith='REQ-TEST-')
        if old_test_requests.exists():
            for old_request in old_test_requests:
                print(f"Deleting old test request: {old_request.request_id}")
                old_request.delete()
    except Exception as e:
        print(f"Error checking for old test requests: {e}")
    
    # Create a new test request
    test_request = SupplyRequest.objects.create(
        request_id=test_request_id,
        requester=test_user,
        department='IT',
        status='Pending',
        justification='Test justification for request model test',
        requested_date=requested_date,
        required_date=required_date,
        notes='Test notes for request model test'
    )
    print(f"Created new supply request: {test_request_id}")
    
    # Test creating a request item
    print("Creating test request item...")
    test_request_item = RequestItem.objects.create(
        request=test_request,
        supply_item=test_supply_item,
        quantity_requested=5,
        unit_price=test_supply_item.unit_cost,
        notes='Test item notes for request model test'
    )
    print(f"Created new request item for {test_request_id}")
    
    # Test properties
    print("Testing properties...")
    assert test_request.total_items == 1, f"Expected total_items to be 1, got {test_request.total_items}"
    assert test_request.total_cost == 50.00, f"Expected total_cost to be 50.00, got {test_request.total_cost}"
    
    print("All tests passed!")
    
    # Clean up test data
    print("Cleaning up test data...")
    try:
        # Only delete the objects we created in this test run
        if 'test_request_item' in locals():
            print(f"Deleting test request item...")
            test_request_item.delete()
        
        if 'test_request' in locals():
            print(f"Deleting test request: {test_request.request_id}")
            test_request.delete()
        
        # We don't delete the supply item, category, or user since they might be used by other tests
        print("Note: Not deleting supply item, category, or user as they may be used by other tests.")
    except Exception as e:
        print(f"Error during cleanup: {e}")
        print("Continuing despite cleanup error...")
    
    print("Test completed successfully!")

if __name__ == '__main__':
    test_supply_request_model()