{% extends 'base.html' %}

{% block title %}Review Supply Request{% endblock %}

{% block header_title %}Review Request #{{ supply_request.request_id }}{% endblock %}

{% block breadcrumb_items %}
<li>
    <div class="flex items-center">
        <svg class="flex-shrink-0 h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
        </svg>
        <a href="{% url 'suptrack:dashboard' %}" class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700">Dashboard</a>
    </div>
</li>
<li>
    <div class="flex items-center">
        <svg class="flex-shrink-0 h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
        </svg>
        <span class="ml-4 text-sm font-medium text-gray-500">Review Request</span>
    </div>
</li>
{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="mb-8">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Review Supply Request</h1>
            <div class="mt-2 flex items-center space-x-4">
                <span class="text-lg font-medium text-gray-600">{{ supply_request.request_id }}</span>
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                    <svg class="-ml-0.5 mr-1.5 h-2 w-2 text-yellow-400" fill="currentColor" viewBox="0 0 8 8">
                        <circle cx="4" cy="4" r="3" />
                    </svg>
                    {{ supply_request.get_status_display }}
                </span>
                <span class="text-sm text-gray-500">{{ supply_request.total_items }} item{{ supply_request.total_items|pluralize }}</span>
                <span class="text-sm text-gray-500">${{ supply_request.total_cost|floatformat:2 }}</span>
            </div>
        </div>
        <div class="mt-4 sm:mt-0">
            <a href="{% url 'suptrack:dashboard' %}"
               class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Dashboard
            </a>
        </div>
    </div>
</div>

<!-- Request Overview Cards -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
    <!-- Request Details Card -->
    <div class="bg-white shadow-lg rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <h3 class="text-lg font-semibold text-gray-900">Request Details</h3>
        </div>
        <div class="px-6 py-4">
            <dl class="space-y-4">
                <div>
                    <dt class="text-sm font-medium text-gray-500">Requester</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ supply_request.requester.get_full_name|default:supply_request.requester.username }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Department</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ supply_request.department }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Requested Date</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ supply_request.requested_date|date:"M d, Y g:i A" }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Required Date</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ supply_request.required_date|date:"M d, Y" }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Days Since Request</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ supply_request.requested_date|timesince }}</dd>
                </div>
            </dl>
        </div>
    </div>

    <!-- Justification Card -->
    <div class="bg-white shadow-lg rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <h3 class="text-lg font-semibold text-gray-900">Justification</h3>
        </div>
        <div class="px-6 py-4">
            <p class="text-sm text-gray-700 leading-relaxed">{{ supply_request.justification }}</p>
        </div>
    </div>

    <!-- Summary Card -->
    <div class="bg-white shadow-lg rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <h3 class="text-lg font-semibold text-gray-900">Summary</h3>
        </div>
        <div class="px-6 py-4">
            <dl class="space-y-4">
                <div>
                    <dt class="text-sm font-medium text-gray-500">Total Items</dt>
                    <dd class="mt-1 text-2xl font-bold text-gray-900">{{ supply_request.total_items }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Estimated Cost</dt>
                    <dd class="mt-1 text-2xl font-bold text-green-600">${{ supply_request.total_cost|floatformat:2 }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Urgency</dt>
                    <dd class="mt-1">
                        {% now "Y-m-d" as today %}
                        {% if supply_request.required_date|date:"Y-m-d" <= today %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                Overdue
                            </span>
                        {% elif supply_request.required_date|timeuntil|slice:":1" == "1" %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                Due Soon
                            </span>
                        {% else %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                Normal
                            </span>
                        {% endif %}
                    </dd>
                </div>
            </dl>
        </div>
    </div>
</div>

<!-- Items Review Section -->
<div class="bg-white shadow-lg rounded-lg overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900">Items Review & Stock Validation</h3>
            <span class="text-sm text-gray-500">{{ supply_request.request_items.count }} item{{ supply_request.request_items.count|pluralize }}</span>
        </div>
    </div>

    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Item Details
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Requested Qty
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Current Stock
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Unit Price
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Total Cost
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                    </th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for item in supply_request.request_items.all %}
                    <tr class="hover:bg-gray-50 transition-colors duration-150">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10">
                                    <div class="h-10 w-10 rounded-lg bg-blue-100 flex items-center justify-center">
                                        <svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                        </svg>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-gray-900">{{ item.supply_item.name }}</div>
                                    <div class="text-sm text-gray-500">{{ item.supply_item.item_code }}</div>
                                    {% if item.supply_item.description %}
                                        <div class="text-xs text-gray-400 mt-1">{{ item.supply_item.description|truncatechars:50 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900 font-medium">{{ item.quantity_requested }}</div>
                            <div class="text-sm text-gray-500">{{ item.supply_item.unit_of_measure }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">{{ item.supply_item.current_stock }}</div>
                            {% if item.supply_item.current_stock < item.quantity_requested %}
                                <div class="text-xs text-red-600 font-medium">Insufficient stock</div>
                            {% elif item.supply_item.current_stock <= item.supply_item.reorder_level %}
                                <div class="text-xs text-yellow-600 font-medium">Low stock</div>
                            {% else %}
                                <div class="text-xs text-green-600 font-medium">Available</div>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">${{ item.unit_price|floatformat:2 }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">${{ item.total_cost|floatformat:2 }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if item.supply_item.current_stock < item.quantity_requested %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    <svg class="-ml-0.5 mr-1.5 h-2 w-2 text-red-400" fill="currentColor" viewBox="0 0 8 8">
                                        <circle cx="4" cy="4" r="3" />
                                    </svg>
                                    Cannot Fulfill
                                </span>
                            {% elif item.supply_item.current_stock <= item.supply_item.reorder_level %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                    <svg class="-ml-0.5 mr-1.5 h-2 w-2 text-yellow-400" fill="currentColor" viewBox="0 0 8 8">
                                        <circle cx="4" cy="4" r="3" />
                                    </svg>
                                    Low Stock
                                </span>
                            {% else %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <svg class="-ml-0.5 mr-1.5 h-2 w-2 text-green-400" fill="currentColor" viewBox="0 0 8 8">
                                        <circle cx="4" cy="4" r="3" />
                                    </svg>
                                    Available
                                </span>
                            {% endif %}
                        </td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- Items Summary -->
    <div class="bg-gray-50 px-6 py-4 border-t border-gray-200">
        <div class="flex justify-between items-center">
            <div class="text-sm text-gray-600">
                <strong>Total:</strong> {{ supply_request.total_items }} item{{ supply_request.total_items|pluralize }}
            </div>
            <div class="text-lg font-semibold text-gray-900">
                ${{ supply_request.total_cost|floatformat:2 }}
            </div>
        </div>
    </div>
</div>

<!-- Stock Validation Alert -->
{% with insufficient_items=supply_request.request_items.all|length %}
    {% for item in supply_request.request_items.all %}
        {% if item.supply_item.current_stock < item.quantity_requested %}
            <div class="mt-6 bg-red-50 border border-red-200 rounded-md p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-red-800">Stock Validation Issues</h3>
                        <div class="mt-2 text-sm text-red-700">
                            <p>Some items in this request cannot be fulfilled due to insufficient stock:</p>
                            <ul class="list-disc list-inside mt-2">
                                {% for item in supply_request.request_items.all %}
                                    {% if item.supply_item.current_stock < item.quantity_requested %}
                                        <li>{{ item.supply_item.name }}: Requested {{ item.quantity_requested }}, Available {{ item.supply_item.current_stock }}</li>
                                    {% endif %}
                                {% endfor %}
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            {% break %}
        {% endif %}
    {% endfor %}
{% endwith %}

<!-- Approval Workflow -->
<div class="mt-8 bg-white shadow-lg rounded-lg overflow-hidden" x-data="{ showRejectionReason: false, decision: null }">
    <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
        <h3 class="text-lg font-semibold text-gray-900">Review Decision</h3>
        <p class="mt-1 text-sm text-gray-600">Make your decision on this supply request after reviewing all items and stock availability.</p>
    </div>

    <form method="post" class="px-6 py-6">
        {% csrf_token %}

        <!-- Decision Options -->
        <div class="space-y-4">
            <div class="flex items-center space-x-4">
                <div class="flex items-center">
                    <input id="approve" name="decision" type="radio" value="approve"
                           x-model="decision" @change="showRejectionReason = false"
                           class="focus:ring-green-500 h-4 w-4 text-green-600 border-gray-300">
                    <label for="approve" class="ml-3 block text-sm font-medium text-gray-700">
                        <span class="flex items-center">
                            <svg class="h-5 w-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            Approve Request
                        </span>
                    </label>
                </div>

                <div class="flex items-center">
                    <input id="reject" name="decision" type="radio" value="reject"
                           x-model="decision" @change="showRejectionReason = true"
                           class="focus:ring-red-500 h-4 w-4 text-red-600 border-gray-300">
                    <label for="reject" class="ml-3 block text-sm font-medium text-gray-700">
                        <span class="flex items-center">
                            <svg class="h-5 w-5 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            Reject Request
                        </span>
                    </label>
                </div>
            </div>
        </div>

        <!-- Rejection Reason -->
        <div x-show="showRejectionReason"
             x-transition:enter="transition ease-out duration-200"
             x-transition:enter-start="opacity-0 transform scale-95"
             x-transition:enter-end="opacity-100 transform scale-100"
             x-transition:leave="transition ease-in duration-150"
             x-transition:leave-start="opacity-100 transform scale-100"
             x-transition:leave-end="opacity-0 transform scale-95"
             class="mt-6">
            <label for="rejection_reason" class="block text-sm font-medium text-gray-700">
                Reason for Rejection <span class="text-red-500">*</span>
            </label>
            <div class="mt-1">
                <textarea name="rejection_reason" id="rejection_reason" rows="4"
                          placeholder="Please provide a detailed reason for rejecting this request..."
                          class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-red-500 focus:border-red-500 sm:text-sm"></textarea>
            </div>
            <p class="mt-2 text-sm text-gray-500">This reason will be shared with the requester to help them understand the decision.</p>
        </div>

        <!-- Additional Notes -->
        <div class="mt-6">
            <label for="notes" class="block text-sm font-medium text-gray-700">Additional Notes (Optional)</label>
            <div class="mt-1">
                <textarea name="notes" id="notes" rows="3"
                          placeholder="Any additional comments or instructions..."
                          class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"></textarea>
            </div>
            <p class="mt-2 text-sm text-gray-500">These notes will be added to the request for future reference.</p>
        </div>

        <!-- Action Buttons -->
        <div class="mt-8 flex flex-col sm:flex-row gap-4">
            <button type="submit" name="status" value="approved"
                    x-show="decision === 'approve'"
                    class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition duration-150 ease-in-out">
                <svg class="-ml-1 mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                Approve Request
            </button>

            <button type="submit" name="status" value="rejected"
                    x-show="decision === 'reject'"
                    class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition duration-150 ease-in-out">
                <svg class="-ml-1 mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                Reject Request
            </button>

            <button type="button"
                    class="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150 ease-in-out">
                <svg class="-ml-1 mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                Save for Later
            </button>
        </div>

        <!-- Decision Summary -->
        <div x-show="decision" class="mt-6 p-4 rounded-md"
             :class="decision === 'approve' ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg x-show="decision === 'approve'" class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                    <svg x-show="decision === 'reject'" class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium"
                        :class="decision === 'approve' ? 'text-green-800' : 'text-red-800'">
                        <span x-show="decision === 'approve'">Request will be approved</span>
                        <span x-show="decision === 'reject'">Request will be rejected</span>
                    </h3>
                    <div class="mt-2 text-sm"
                         :class="decision === 'approve' ? 'text-green-700' : 'text-red-700'">
                        <p x-show="decision === 'approve'">
                            The requester will be notified of the approval and items will be prepared for release.
                        </p>
                        <p x-show="decision === 'reject'">
                            The requester will be notified of the rejection along with your reason.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<!-- Quick Actions -->
<div class="mt-8 bg-white shadow-lg rounded-lg overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
        <h3 class="text-lg font-semibold text-gray-900">Quick Actions</h3>
    </div>
    <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <a href="{% url 'suptrack:supply_request_detail' pk=supply_request.pk %}"
               class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition duration-150 ease-in-out">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-900">View Full Details</p>
                    <p class="text-sm text-gray-500">See complete request information</p>
                </div>
            </a>

            <a href="{% url 'suptrack:inventory_dashboard' %}"
               class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition duration-150 ease-in-out">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-900">Check Inventory</p>
                    <p class="text-sm text-gray-500">View current stock levels</p>
                </div>
            </a>

            <a href="{% url 'suptrack:dashboard' %}"
               class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition duration-150 ease-in-out">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-900">Back to Dashboard</p>
                    <p class="text-sm text-gray-500">Return to main dashboard</p>
                </div>
            </a>
        </div>
    </div>
</div>
{% endblock %}
