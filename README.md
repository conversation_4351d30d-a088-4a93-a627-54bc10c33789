# SmartSupply Management System

A comprehensive Django-based supply chain management system designed for government and organizational supply tracking, request processing, and inventory management.

## 🚀 Quick Start

### System Requirements
- Python 3.8+
- Django 4.2+
- SQLite (default) or PostgreSQL
- Modern web browser

### Installation & Setup
```bash
# Clone the repository
git clone <repository-url>
cd smartsupply

# Install dependencies
pip install -r requirements.txt

# Run migrations
python manage.py migrate

# Start the development server
python manage.py runserver
```

Access the application at: **http://localhost:8000**

## 🔐 Login Credentials

### Administrator Accounts (Full System Access)
| Username | Password | Email | Role |
|----------|----------|-------|------|
| `admin` | `admin123` | <EMAIL> | Superuser |
| `drr` | `admin123` | <EMAIL> | Superuser |

### GSO Staff Accounts (Supply Management)
| Username | Password | Email | Department | Role |
|----------|----------|-------|------------|------|
| `gsostaff` | `password123` | <EMAIL> | General Services Office | GSO Staff |
| `jane_smith` | `auto-generated` | <EMAIL> | GSO | GSO Staff |
| `mike_davis` | `auto-generated` | <EMAIL> | GSO | GSO Staff |
| `test_gso` | `auto-generated` | <EMAIL> | GSO | GSO Staff |

### Department User Accounts (Request Submission)
| Username | Password | Email | Department | Role |
|----------|----------|-------|------------|------|
| `testuser` | `password123` | <EMAIL> | IT Department | Department User |
| `lanzy` | `password123` | <EMAIL> | MMO | Department User |
| `john_doe` | `auto-generated` | <EMAIL> | IT | Department User |
| `bob_wilson` | `auto-generated` | <EMAIL> | HR | Department User |
| `alice_brown` | `auto-generated` | <EMAIL> | Finance | Department User |

> **Note**: For accounts with "auto-generated" passwords, use the password reset feature or contact the administrator.

## 🎯 User Roles & Permissions

### 🔧 Administrator
- Full system access
- User management
- System configuration
- All GSO and Department user capabilities

### 📋 GSO Staff
- Review and approve/reject supply requests
- Manage inventory items and stock levels
- Generate reports and analytics
- QR code scanning and management
- Access to GSO dashboard with comprehensive metrics

### 👤 Department User
- Submit supply requests
- Track request status
- View department-specific dashboard
- Access to personal request history

## 🏠 Dashboard Access

### GSO Dashboard
- **URL**: `/dashboard/gso/`
- **Access**: GSO Staff and Administrators
- **Features**:
  - Pending requests overview
  - Low stock alerts
  - Inventory management tools
  - Performance metrics
  - Quick actions panel

### Department Dashboard
- **URL**: `/dashboard/department/`
- **Access**: Department Users and above
- **Features**:
  - Submit new requests
  - Track request status
  - Department statistics
  - Recent activity

### Admin Dashboard
- **URL**: `/admin/`
- **Access**: Administrators only
- **Features**:
  - User management
  - System configuration
  - Database administration

## 📦 Sample Data

The system comes pre-loaded with sample data:

### Inventory Items (8 items)
- **Electronics**: Laptop Computer, Wireless Mouse
- **Furniture**: Office Chair, Desk Lamp
- **Stationery**: Printer Paper, Blue Pens
- **Cleaning**: All-Purpose Cleaner, Paper Towels

### Categories
- Electronics
- Furniture
- Stationery
- Cleaning

## 🛠 Management Commands

The system includes powerful management commands for administrative tasks:

### User Management
```bash
# Create a new user
python manage.py manage_users create --username newuser --email <EMAIL> --role department_user --department IT

# Bulk import users from CSV
python manage.py manage_users bulk_import --file users.csv

# List all users
python manage.py manage_users list --format table

# Generate user activity report
python manage.py manage_users report --days 30 --output report.csv
```

### Inventory Management
```bash
# Import inventory from CSV
python manage.py manage_inventory import --file inventory.csv

# List inventory items
python manage.py manage_inventory list --format table

# Update stock levels
python manage.py manage_inventory update_stock --item_code IT001 --quantity 100 --reason "Replenishment"

# Generate low stock report
python manage.py manage_inventory low_stock --output low_stock.csv

# Export inventory
python manage.py manage_inventory export --file current_inventory.csv
```

## 🔄 Workflow Overview

### Supply Request Process
1. **Department User** submits supply request
2. **GSO Staff** reviews request details
3. **GSO Staff** approves/rejects with comments
4. **System** updates inventory levels (if approved)
5. **Department User** receives notification of decision

### Inventory Management
1. **GSO Staff** monitors stock levels
2. **System** generates low stock alerts
3. **GSO Staff** updates inventory as needed
4. **QR Codes** enable quick item identification
5. **Reports** provide usage analytics

## 📱 Key Features

### ✅ Supply Request Management
- Online request submission
- Multi-item requests with quantities
- Approval workflow with comments
- Status tracking and notifications
- Request history and analytics

### ✅ Inventory Management
- Real-time stock tracking
- Low stock alerts and notifications
- QR code integration for quick scanning
- Category-based organization
- Supplier and location tracking

### ✅ User Management
- Role-based access control
- Department-based organization
- User activity tracking
- Bulk user operations
- Profile management

### ✅ Reporting & Analytics
- Usage pattern analysis
- Cost tracking and budgeting
- Performance metrics
- Export capabilities (CSV, JSON)
- Custom date range filtering

### ✅ Modern UI/UX
- Responsive design for all devices
- Professional dashboard interfaces
- Real-time status updates
- Intuitive navigation
- Accessibility features

## 🔧 Technical Details

### Technology Stack
- **Backend**: Django 4.2, Python 3.8+
- **Database**: SQLite (development), PostgreSQL (production)
- **Frontend**: HTML5, CSS3, JavaScript, Tailwind CSS
- **Authentication**: Django built-in authentication
- **File Processing**: CSV/Excel import/export
- **QR Codes**: Python QR code generation

### Database Models
- **User & UserProfile**: Extended user management
- **SupplyItem**: Inventory item tracking
- **SupplyRequest & RequestItem**: Request workflow
- **Category**: Item categorization
- **StockTransaction**: Inventory movement tracking

## 📞 Support & Documentation

### Getting Help
- Check the built-in help system in the application
- Review management command documentation in `suptrack/management/commands/README.md`
- Contact system administrators for account issues

### Development
- Follow Django best practices
- Use the provided management commands for data operations
- Test changes in development environment first

## 🔒 Security Notes

- Change default passwords in production
- Use strong passwords for all accounts
- Regularly backup the database
- Monitor user activity through reports
- Keep the system updated

## 🚀 Quick Login Guide

### For GSO Staff (Supply Management)
1. Go to **http://localhost:8000**
2. Click **"Login"**
3. Use credentials: `gsostaff` / `password123`
4. Access GSO Dashboard for supply management

### For Department Users (Submit Requests)
1. Go to **http://localhost:8000**
2. Click **"Login"**
3. Use credentials: `testuser` / `password123`
4. Access Department Dashboard to submit requests

### For Administrators (Full Access)
1. Go to **http://localhost:8000/admin/**
2. Use credentials: `admin` / `admin123`
3. Full system administration access

## 📋 Common Tasks

### Submit a Supply Request (Department Users)
1. Login as department user
2. Navigate to "Submit Request"
3. Fill in request details and justification
4. Add required items with quantities
5. Submit for GSO review

### Process Supply Requests (GSO Staff)
1. Login as GSO staff
2. Go to GSO Dashboard
3. Review pending requests
4. Click "Review" on any request
5. Approve/Reject with comments

### Manage Inventory (GSO Staff)
1. Login as GSO staff
2. Navigate to "Inventory Management"
3. View current stock levels
4. Update quantities as needed
5. Monitor low stock alerts

## 🔄 Password Reset

If you need to reset passwords for any account:

```bash
# Reset password for specific user
python manage.py manage_users reset_password --username [username] --password [new_password]

# Example
python manage.py manage_users reset_password --username john_doe --password newpassword123
```

## 📊 Sample Workflows

### Scenario 1: IT Department Needs Laptops
1. **testuser** (IT Dept) logs in
2. Submits request for 5 laptops
3. **gsostaff** reviews and approves
4. Inventory automatically updated
5. **testuser** receives approval notification

### Scenario 2: Low Stock Alert
1. System detects low stock on office chairs
2. **gsostaff** receives alert on dashboard
3. Updates inventory after receiving new shipment
4. Stock levels return to normal

### Scenario 3: Monthly Reporting
1. **gsostaff** generates usage reports
2. Reviews department spending patterns
3. Identifies frequently requested items
4. Plans future procurement accordingly

---

**SmartSupply Management System** - Streamlining supply chain operations with modern technology.

*Last Updated: July 2025*
