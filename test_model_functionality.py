import os
import sys
import django
import uuid
from decimal import Decimal
from datetime import datetime, timedelta

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smartsupply.settings')
django.setup()

from django.contrib.auth.models import User
from django.utils import timezone
from suptrack.models import (
    UserProfile, Category, SupplyItem, SupplyRequest, 
    RequestItem, StockTransaction, QRCode, ScanActivity
)

def test_model_functionality():
    print("\n=== Testing Model Functionality ===\n")
    
    # Get or create test user
    try:
        test_user = User.objects.get(username='test_functionality')
        print(f"Using existing user: {test_user.username}")
    except User.DoesNotExist:
        test_user = User.objects.create_user(
            username='test_functionality',
            email='<EMAIL>',
            password='testpassword'
        )
        print(f"Created new user: {test_user.username}")
    
    # Get or create test category
    try:
        test_category = Category.objects.get(name='Test Functionality Category')
        print(f"Using existing category: {test_category.name}")
    except Category.DoesNotExist:
        test_category = Category.objects.create(
            name='Test Functionality Category',
            description='Category for testing model functionality'
        )
        print(f"Created new category: {test_category.name}")
    
    # Get or create test supply items
    item_code1 = f"ITEM-{uuid.uuid4().hex[:8].upper()}"
    item_code2 = f"ITEM-{uuid.uuid4().hex[:8].upper()}"
    
    try:
        test_item1 = SupplyItem.objects.get(name='Test Functionality Item 1')
        print(f"Using existing supply item 1: {test_item1.name}")
    except SupplyItem.DoesNotExist:
        test_item1 = SupplyItem.objects.create(
            item_code=item_code1,
            name='Test Functionality Item 1',
            description='Item 1 for testing model functionality',
            category=test_category,
            unit_of_measure='each',
            current_stock=100,
            minimum_threshold=20,
            unit_cost=Decimal('10.50'),
            supplier='Test Supplier'
        )
        print(f"Created new supply item 1: {test_item1.name} with code {test_item1.item_code}")
    
    try:
        test_item2 = SupplyItem.objects.get(name='Test Functionality Item 2')
        print(f"Using existing supply item 2: {test_item2.name}")
    except SupplyItem.DoesNotExist:
        test_item2 = SupplyItem.objects.create(
            item_code=item_code2,
            name='Test Functionality Item 2',
            description='Item 2 for testing model functionality',
            category=test_category,
            unit_of_measure='box',
            current_stock=50,
            minimum_threshold=10,
            unit_cost=Decimal('15.75'),
            supplier='Test Supplier'
        )
        print(f"Created new supply item 2: {test_item2.name} with code {test_item2.item_code}")
    
    # Test 1: Test SupplyItem.is_low_stock property
    print("\nTest 1: Testing SupplyItem.is_low_stock property")
    original_stock = test_item1.current_stock
    original_threshold = test_item1.minimum_threshold
    
    print(f"Original stock: {original_stock}, threshold: {original_threshold}")
    print(f"is_low_stock: {test_item1.is_low_stock}")
    
    # Temporarily set stock below threshold
    test_item1.current_stock = test_item1.minimum_threshold - 5
    test_item1.save()
    print(f"After setting stock to {test_item1.current_stock}")
    print(f"is_low_stock: {test_item1.is_low_stock}")
    
    # Restore original values
    test_item1.current_stock = original_stock
    test_item1.save()
    print(f"After restoring stock to {test_item1.current_stock}")
    print(f"is_low_stock: {test_item1.is_low_stock}")
    
    # Test 2: Test SupplyItem.total_value property
    print("\nTest 2: Testing SupplyItem.total_value property")
    print(f"Current stock: {test_item1.current_stock}, unit cost: {test_item1.unit_cost}")
    print(f"total_value: {test_item1.total_value}")
    expected_value = test_item1.current_stock * test_item1.unit_cost
    print(f"Expected value: {expected_value}")
    print(f"Match: {test_item1.total_value == expected_value}")
    
    # Test 3: Create and test SupplyRequest and RequestItem
    print("\nTest 3: Testing SupplyRequest and RequestItem")
    
    # Generate a unique request ID
    request_id = f"REQ-FUNC-{uuid.uuid4().hex[:8].upper()}"
    
    # Delete any old test requests
    old_requests = SupplyRequest.objects.filter(request_id__startswith='REQ-FUNC-')
    if old_requests.exists():
        for req in old_requests:
            print(f"Deleting old test request: {req.request_id}")
            req.delete()
    
    # Create a new supply request
    test_request = SupplyRequest.objects.create(
        request_id=request_id,
        requester=test_user,
        department='Test Department',
        status='pending',
        justification='Test justification',
        required_date=timezone.now().date() + timedelta(days=7),
        notes='Test request for functionality testing'
    )
    print(f"Created new supply request: {test_request.request_id}")
    
    # Create request items
    test_request_item1 = RequestItem.objects.create(
        request=test_request,
        supply_item=test_item1,
        quantity_requested=5,
        unit_price=test_item1.unit_cost
    )
    print(f"Created request item 1: {test_request_item1.id}")
    
    test_request_item2 = RequestItem.objects.create(
        request=test_request,
        supply_item=test_item2,
        quantity_requested=3,
        unit_price=test_item2.unit_cost
    )
    print(f"Created request item 2: {test_request_item2.id}")
    
    # Test SupplyRequest properties
    print(f"SupplyRequest.total_items: {test_request.total_items}")
    print(f"Expected total items: 2")  # Count of request items, not sum of quantities
    print(f"Match: {test_request.total_items == 2}")
    
    print(f"SupplyRequest.total_cost: {test_request.total_cost}")
    expected_cost = (5 * test_item1.unit_cost) + (3 * test_item2.unit_cost)  # 5 and 3 are the quantities requested
    print(f"Expected total cost: {expected_cost}")
    print(f"Match: {float(test_request.total_cost) == float(expected_cost)}")
    
    # Test 4: Test RequestItem properties
    print("\nTest 4: Testing RequestItem properties")
    print(f"RequestItem.total_cost: {test_request_item1.total_cost}")
    expected_item_cost = 5 * test_item1.unit_cost  # 5 is the quantity requested
    print(f"Expected item cost: {expected_item_cost}")
    print(f"Match: {float(test_request_item1.total_cost) == float(expected_item_cost)}")
    
    # Test approved quantities
    test_request_item1.quantity_approved = 4
    test_request_item1.save()
    print(f"After setting approved quantity to {test_request_item1.quantity_approved}")
    print(f"RequestItem.approved_total_cost: {test_request_item1.approved_total_cost}")
    expected_approved_cost = 4 * test_item1.unit_cost
    print(f"Expected approved cost: {expected_approved_cost}")
    print(f"Match: {float(test_request_item1.approved_total_cost) == float(expected_approved_cost)}")
    
    # Test 5: Test StockTransaction
    print("\nTest 5: Testing StockTransaction")
    
    # Record original stock level
    original_stock = test_item1.current_stock
    print(f"Original stock level: {original_stock}")
    
    # Create a stock transaction (addition)
    transaction_add = StockTransaction.objects.create(
        supply_item=test_item1,
        transaction_type='in',
        quantity=10,
        performed_by=test_user,
        notes='Test addition transaction'
    )
    print(f"Created addition transaction: {transaction_add.id}")
    
    # Refresh item from database
    test_item1.refresh_from_db()
    print(f"Stock level after addition: {test_item1.current_stock}")
    print(f"Expected stock level: {original_stock + 10}")
    print(f"Match: {test_item1.current_stock == original_stock + 10}")
    
    # Create a stock transaction (deduction)
    transaction_deduct = StockTransaction.objects.create(
        supply_item=test_item1,
        transaction_type='out',
        quantity=5,
        performed_by=test_user,
        notes='Test deduction transaction'
    )
    print(f"Created deduction transaction: {transaction_deduct.id}")
    
    # Refresh item from database
    test_item1.refresh_from_db()
    print(f"Stock level after deduction: {test_item1.current_stock}")
    print(f"Expected stock level: {original_stock + 10 - 5}")
    print(f"Match: {test_item1.current_stock == original_stock + 10 - 5}")
    
    # Test 6: Test QRCode and ScanActivity
    print("\nTest 6: Testing QRCode and ScanActivity")
    
    # Generate a unique code ID
    code_id = f"{(timezone.now() + timedelta(days=365)).strftime('%Y%m%d')}-{uuid.uuid4().hex[:8].upper()}"
    
    # Create a QR code
    test_qrcode = QRCode.objects.create(
        code_id=code_id,
        supply_item=test_item1,
        request_item=test_request_item1,
        is_active=True
    )
    print(f"Created QR code: {test_qrcode.code_id}")
    
    # Create a scan activity
    test_scan = ScanActivity.objects.create(
        qr_code=test_qrcode,
        scanned_by=test_user,
        scan_type='general',
        scan_data='{"test": "data"}',
        success=True,
        ip_address='127.0.0.1',
        user_agent='Test User Agent'
    )
    print(f"Created scan activity: {test_scan.id}")
    
    # Test QRCode properties
    test_qrcode.refresh_from_db()
    print(f"QRCode.scanned_count: {test_qrcode.scanned_count}")
    print(f"Expected scanned count: 1")
    print(f"Match: {test_qrcode.scanned_count == 1}")
    
    # Manually call record_scan since it's not automatically called by ScanActivity
    test_qrcode.record_scan(test_user)
    test_qrcode.refresh_from_db()
    
    print(f"After manually calling record_scan:")
    print(f"QRCode.scanned_count: {test_qrcode.scanned_count}")
    print(f"QRCode.last_scanned: {test_qrcode.last_scanned}")
    print(f"QRCode.last_scanned_by: {test_qrcode.last_scanned_by.username}")
    
    # Clean up
    print("\nCleaning up test data...")
    test_scan.delete()
    test_qrcode.delete()
    transaction_add.delete()
    transaction_deduct.delete()
    test_request.delete()  # This will cascade delete the request items
    
    # Restore original stock level
    test_item1.current_stock = original_stock
    test_item1.save()
    print(f"Restored stock level to {test_item1.current_stock}")
    
    print("\nFunctionality testing complete! All models are working correctly.")

if __name__ == '__main__':
    test_model_functionality()