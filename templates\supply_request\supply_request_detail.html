{% extends 'base.html' %}

{% block title %}Supply Request Details{% endblock %}

{% block header_title %}Request #{{ supply_request.request_id }}{% endblock %}

{% block breadcrumb_items %}
<li>
    <div class="flex items-center">
        <svg class="flex-shrink-0 h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
        </svg>
        <a href="{% url 'suptrack:supply_request_list' %}" class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700">Supply Requests</a>
    </div>
</li>
<li>
    <div class="flex items-center">
        <svg class="flex-shrink-0 h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
        </svg>
        <span class="ml-4 text-sm font-medium text-gray-500">{{ supply_request.request_id }}</span>
    </div>
</li>
{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="mb-8">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Request #{{ supply_request.request_id }}</h1>
            <div class="mt-2 flex items-center space-x-4">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                    {% if supply_request.status == 'approved' %}
                        bg-green-100 text-green-800
                    {% elif supply_request.status == 'pending' %}
                        bg-yellow-100 text-yellow-800
                    {% elif supply_request.status == 'rejected' %}
                        bg-red-100 text-red-800
                    {% elif supply_request.status == 'completed' %}
                        bg-blue-100 text-blue-800
                    {% elif supply_request.status == 'released' %}
                        bg-purple-100 text-purple-800
                    {% else %}
                        bg-gray-100 text-gray-800
                    {% endif %}">
                    {% if supply_request.status == 'approved' %}
                        <svg class="-ml-0.5 mr-1.5 h-2 w-2 text-green-400" fill="currentColor" viewBox="0 0 8 8">
                            <circle cx="4" cy="4" r="3" />
                        </svg>
                    {% elif supply_request.status == 'pending' %}
                        <svg class="-ml-0.5 mr-1.5 h-2 w-2 text-yellow-400" fill="currentColor" viewBox="0 0 8 8">
                            <circle cx="4" cy="4" r="3" />
                        </svg>
                    {% elif supply_request.status == 'rejected' %}
                        <svg class="-ml-0.5 mr-1.5 h-2 w-2 text-red-400" fill="currentColor" viewBox="0 0 8 8">
                            <circle cx="4" cy="4" r="3" />
                        </svg>
                    {% elif supply_request.status == 'completed' %}
                        <svg class="-ml-0.5 mr-1.5 h-2 w-2 text-blue-400" fill="currentColor" viewBox="0 0 8 8">
                            <circle cx="4" cy="4" r="3" />
                        </svg>
                    {% endif %}
                    {{ supply_request.get_status_display }}
                </span>
                <span class="text-sm text-gray-500">{{ supply_request.total_items }} item{{ supply_request.total_items|pluralize }}</span>
                <span class="text-sm text-gray-500">${{ supply_request.total_cost|floatformat:2 }}</span>
            </div>
        </div>
        <div class="mt-4 sm:mt-0 flex space-x-3">
            <a href="{% url 'suptrack:supply_request_list' %}"
               class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to List
            </a>
            {% if supply_request.status == 'pending' %}
                <button type="button"
                        class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                    Edit Request
                </button>
            {% endif %}
        </div>
    </div>
</div>

<!-- Request Overview Cards -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
    <!-- Request Details Card -->
    <div class="bg-white shadow-lg rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <h3 class="text-lg font-semibold text-gray-900">Request Details</h3>
        </div>
        <div class="px-6 py-4">
            <dl class="space-y-4">
                <div>
                    <dt class="text-sm font-medium text-gray-500">Department</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ supply_request.department }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Requester</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ supply_request.requester.get_full_name|default:supply_request.requester.username }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Requested Date</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ supply_request.requested_date|date:"M d, Y g:i A" }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Required Date</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ supply_request.required_date|date:"M d, Y" }}</dd>
                </div>
                {% if supply_request.approved_by %}
                <div>
                    <dt class="text-sm font-medium text-gray-500">Approved By</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ supply_request.approved_by.get_full_name|default:supply_request.approved_by.username }}</dd>
                </div>
                {% endif %}
                {% if supply_request.approved_date %}
                <div>
                    <dt class="text-sm font-medium text-gray-500">Approved Date</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ supply_request.approved_date|date:"M d, Y g:i A" }}</dd>
                </div>
                {% endif %}
            </dl>
        </div>
    </div>

    <!-- Justification Card -->
    <div class="bg-white shadow-lg rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <h3 class="text-lg font-semibold text-gray-900">Justification</h3>
        </div>
        <div class="px-6 py-4">
            <p class="text-sm text-gray-700 leading-relaxed">{{ supply_request.justification }}</p>
        </div>
    </div>

    <!-- Summary Card -->
    <div class="bg-white shadow-lg rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <h3 class="text-lg font-semibold text-gray-900">Summary</h3>
        </div>
        <div class="px-6 py-4">
            <dl class="space-y-4">
                <div>
                    <dt class="text-sm font-medium text-gray-500">Total Items</dt>
                    <dd class="mt-1 text-2xl font-bold text-gray-900">{{ supply_request.total_items }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Estimated Cost</dt>
                    <dd class="mt-1 text-2xl font-bold text-green-600">${{ supply_request.total_cost|floatformat:2 }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Days Since Request</dt>
                    <dd class="mt-1 text-lg font-semibold text-gray-900">{{ supply_request.requested_date|timesince }}</dd>
                </div>
            </dl>
        </div>
    </div>
</div>

<!-- Request Items Section -->
<div class="bg-white shadow-lg rounded-lg overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900">Requested Items</h3>
            <span class="text-sm text-gray-500">{{ supply_request.request_items.count }} item{{ supply_request.request_items.count|pluralize }}</span>
        </div>
    </div>

    {% if supply_request.request_items.exists %}
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Item Details
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Quantity
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Unit Price
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Total Cost
                        </th>
                        {% if supply_request.status == 'approved' or supply_request.status == 'completed' %}
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Approved Qty
                        </th>
                        {% endif %}
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for item in supply_request.request_items.all %}
                        <tr class="hover:bg-gray-50 transition-colors duration-150">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <div class="h-10 w-10 rounded-lg bg-blue-100 flex items-center justify-center">
                                            <svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">{{ item.supply_item.name }}</div>
                                        <div class="text-sm text-gray-500">{{ item.supply_item.item_code }}</div>
                                        {% if item.supply_item.description %}
                                            <div class="text-xs text-gray-400 mt-1">{{ item.supply_item.description|truncatechars:50 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">{{ item.quantity_requested }}</div>
                                <div class="text-sm text-gray-500">{{ item.supply_item.unit_of_measure }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">${{ item.unit_price|floatformat:2 }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">${{ item.total_cost|floatformat:2 }}</div>
                            </td>
                            {% if supply_request.status == 'approved' or supply_request.status == 'completed' %}
                            <td class="px-6 py-4 whitespace-nowrap">
                                {% if item.quantity_approved %}
                                    <div class="text-sm text-green-600 font-medium">{{ item.quantity_approved }}</div>
                                    <div class="text-xs text-gray-500">${{ item.approved_total_cost|floatformat:2 }}</div>
                                {% else %}
                                    <span class="text-sm text-gray-400">Not set</span>
                                {% endif %}
                            </td>
                            {% endif %}
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Items Summary -->
        <div class="bg-gray-50 px-6 py-4 border-t border-gray-200">
            <div class="flex justify-between items-center">
                <div class="text-sm text-gray-600">
                    <strong>Total:</strong> {{ supply_request.total_items }} item{{ supply_request.total_items|pluralize }}
                </div>
                <div class="text-lg font-semibold text-gray-900">
                    ${{ supply_request.total_cost|floatformat:2 }}
                </div>
            </div>
        </div>
    {% else %}
        <div class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No items in this request</h3>
            <p class="mt-1 text-sm text-gray-500">This request doesn't have any items yet.</p>
        </div>
    {% endif %}
</div>

<!-- Status Timeline -->
<div class="mt-8 bg-white shadow-lg rounded-lg overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
        <h3 class="text-lg font-semibold text-gray-900">Request Timeline</h3>
    </div>
    <div class="px-6 py-6">
        <div class="flow-root">
            <ul class="-mb-8">
                <!-- Request Created -->
                <li>
                    <div class="relative pb-8">
                        <span class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200" aria-hidden="true"></span>
                        <div class="relative flex space-x-3">
                            <div>
                                <span class="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center ring-8 ring-white">
                                    <svg class="h-5 w-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                    </svg>
                                </span>
                            </div>
                            <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                <div>
                                    <p class="text-sm text-gray-500">Request created by <span class="font-medium text-gray-900">{{ supply_request.requester.get_full_name|default:supply_request.requester.username }}</span></p>
                                </div>
                                <div class="text-right text-sm whitespace-nowrap text-gray-500">
                                    {{ supply_request.requested_date|date:"M d, Y g:i A" }}
                                </div>
                            </div>
                        </div>
                    </div>
                </li>

                <!-- Status Updates -->
                {% if supply_request.status == 'approved' or supply_request.status == 'completed' %}
                <li>
                    <div class="relative pb-8">
                        {% if supply_request.status == 'completed' %}
                            <span class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200" aria-hidden="true"></span>
                        {% endif %}
                        <div class="relative flex space-x-3">
                            <div>
                                <span class="h-8 w-8 rounded-full bg-green-500 flex items-center justify-center ring-8 ring-white">
                                    <svg class="h-5 w-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                </span>
                            </div>
                            <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                <div>
                                    <p class="text-sm text-gray-500">Request approved by <span class="font-medium text-gray-900">{{ supply_request.approved_by.get_full_name|default:supply_request.approved_by.username }}</span></p>
                                </div>
                                <div class="text-right text-sm whitespace-nowrap text-gray-500">
                                    {{ supply_request.approved_date|date:"M d, Y g:i A" }}
                                </div>
                            </div>
                        </div>
                    </div>
                </li>
                {% endif %}

                {% if supply_request.status == 'completed' %}
                <li>
                    <div class="relative">
                        <div class="relative flex space-x-3">
                            <div>
                                <span class="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center ring-8 ring-white">
                                    <svg class="h-5 w-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                    </svg>
                                </span>
                            </div>
                            <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                <div>
                                    <p class="text-sm text-gray-500">Request completed and items delivered</p>
                                </div>
                                <div class="text-right text-sm whitespace-nowrap text-gray-500">
                                    Recently
                                </div>
                            </div>
                        </div>
                    </div>
                </li>
                {% elif supply_request.status == 'rejected' %}
                <li>
                    <div class="relative">
                        <div class="relative flex space-x-3">
                            <div>
                                <span class="h-8 w-8 rounded-full bg-red-500 flex items-center justify-center ring-8 ring-white">
                                    <svg class="h-5 w-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                    </svg>
                                </span>
                            </div>
                            <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                <div>
                                    <p class="text-sm text-gray-500">Request rejected</p>
                                    {% if supply_request.rejection_reason %}
                                        <p class="text-sm text-red-600 mt-1">{{ supply_request.rejection_reason }}</p>
                                    {% endif %}
                                </div>
                                <div class="text-right text-sm whitespace-nowrap text-gray-500">
                                    Recently
                                </div>
                            </div>
                        </div>
                    </div>
                </li>
                {% endif %}
            </ul>
        </div>
    </div>
</div>

<!-- Additional Notes -->
{% if supply_request.notes %}
<div class="mt-8 bg-white shadow-lg rounded-lg overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
        <h3 class="text-lg font-semibold text-gray-900">Additional Notes</h3>
    </div>
    <div class="px-6 py-4">
        <p class="text-sm text-gray-700 leading-relaxed">{{ supply_request.notes }}</p>
    </div>
</div>
{% endif %}

<!-- Action Buttons -->
{% if supply_request.status == 'pending' %}
<div class="mt-8 bg-white shadow-lg rounded-lg overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
        <h3 class="text-lg font-semibold text-gray-900">Actions</h3>
    </div>
    <div class="px-6 py-4">
        <div class="flex flex-col sm:flex-row gap-4">
            <button type="button"
                    class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
                Edit Request
            </button>
            <button type="button"
                    class="inline-flex items-center px-4 py-2 border border-red-300 rounded-md shadow-sm text-sm font-medium text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
                Cancel Request
            </button>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}


