"""
Django management command for comprehensive user account management in SmartSupply.

This command provides functionality for:
- Creating, updating, and deleting user accounts with proper role assignments
- Bulk importing users from CSV files with validation
- Resetting user passwords and sending email notifications
- Assigning users to specific departments
- Generating user activity reports
- Handling user profile creation with proper GSO permissions

Usage Examples:
    # Create a new user
    python manage.py manage_users create --username john_doe --email <EMAIL> --role department_user --department IT

    # Bulk import users from CSV
    python manage.py manage_users bulk_import --file users.csv --send_emails

    # Reset user password
    python manage.py manage_users reset_password --username john_doe --send_email

    # Generate user activity report
    python manage.py manage_users report --output users_report.csv --days 30

    # Update user role
    python manage.py manage_users update --username john_doe --role gso_staff

    # Delete user account
    python manage.py manage_users delete --username john_doe --confirm
"""

import csv
import logging
import os
import secrets
import string
from datetime import datetime, timedelta
from typing import Dict, List, Optional

from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from django.core.mail import send_mail
from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from django.utils import timezone
from django.conf import settings

from suptrack.models import UserProfile, SupplyRequest

# Configure logging
logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Comprehensive user account management for SmartSupply application'

    def add_arguments(self, parser):
        """Add command line arguments"""
        subparsers = parser.add_subparsers(dest='action', help='Available actions')

        # Create user subcommand
        create_parser = subparsers.add_parser('create', help='Create a new user account')
        create_parser.add_argument('--username', required=True, help='Username for the new user')
        create_parser.add_argument('--email', required=True, help='Email address for the new user')
        create_parser.add_argument('--first_name', help='First name of the user')
        create_parser.add_argument('--last_name', help='Last name of the user')
        create_parser.add_argument('--role', required=True, 
                                 choices=['admin', 'gso_staff', 'department_user'],
                                 help='Role for the new user')
        create_parser.add_argument('--department', required=True, help='Department for the new user')
        create_parser.add_argument('--phone', help='Phone number for the user')
        create_parser.add_argument('--password', help='Password for the user (auto-generated if not provided)')
        create_parser.add_argument('--send_email', action='store_true', 
                                 help='Send welcome email with credentials')

        # Update user subcommand
        update_parser = subparsers.add_parser('update', help='Update an existing user account')
        update_parser.add_argument('--username', required=True, help='Username of the user to update')
        update_parser.add_argument('--email', help='New email address')
        update_parser.add_argument('--first_name', help='New first name')
        update_parser.add_argument('--last_name', help='New last name')
        update_parser.add_argument('--role', choices=['admin', 'gso_staff', 'department_user'],
                                  help='New role for the user')
        update_parser.add_argument('--department', help='New department for the user')
        update_parser.add_argument('--phone', help='New phone number')
        update_parser.add_argument('--active', type=bool, help='Set user active status (True/False)')

        # Delete user subcommand
        delete_parser = subparsers.add_parser('delete', help='Delete a user account')
        delete_parser.add_argument('--username', required=True, help='Username of the user to delete')
        delete_parser.add_argument('--confirm', action='store_true', required=True,
                                 help='Confirm deletion (required for safety)')

        # Bulk import subcommand
        bulk_parser = subparsers.add_parser('bulk_import', help='Bulk import users from CSV file')
        bulk_parser.add_argument('--file', required=True, help='Path to CSV file containing user data')
        bulk_parser.add_argument('--send_emails', action='store_true', 
                               help='Send welcome emails to imported users')
        bulk_parser.add_argument('--dry_run', action='store_true', 
                               help='Validate CSV without creating users')

        # Reset password subcommand
        reset_parser = subparsers.add_parser('reset_password', help='Reset user password')
        reset_parser.add_argument('--username', required=True, help='Username of the user')
        reset_parser.add_argument('--password', help='New password (auto-generated if not provided)')
        reset_parser.add_argument('--send_email', action='store_true', 
                                help='Send email with new password')

        # Generate report subcommand
        report_parser = subparsers.add_parser('report', help='Generate user activity report')
        report_parser.add_argument('--output', help='Output file path (default: stdout)')
        report_parser.add_argument('--format', choices=['csv', 'json'], default='csv',
                                 help='Output format')
        report_parser.add_argument('--days', type=int, default=30,
                                 help='Number of days to include in report')
        report_parser.add_argument('--role', choices=['admin', 'gso_staff', 'department_user'],
                                 help='Filter by user role')
        report_parser.add_argument('--department', help='Filter by department')

        # List users subcommand
        list_parser = subparsers.add_parser('list', help='List users with filtering options')
        list_parser.add_argument('--role', choices=['admin', 'gso_staff', 'department_user'],
                               help='Filter by user role')
        list_parser.add_argument('--department', help='Filter by department')
        list_parser.add_argument('--active', type=bool, help='Filter by active status')
        list_parser.add_argument('--format', choices=['table', 'csv'], default='table',
                               help='Output format')

    def handle(self, *args, **options):
        """Main command handler"""
        action = options.get('action')
        
        if not action:
            self.print_help('manage.py', 'manage_users')
            return

        try:
            if action == 'create':
                self.create_user(options)
            elif action == 'update':
                self.update_user(options)
            elif action == 'delete':
                self.delete_user(options)
            elif action == 'bulk_import':
                self.bulk_import_users(options)
            elif action == 'reset_password':
                self.reset_password(options)
            elif action == 'report':
                self.generate_report(options)
            elif action == 'list':
                self.list_users(options)
            else:
                raise CommandError(f"Unknown action: {action}")
                
        except Exception as e:
            logger.error(f"Command failed: {str(e)}")
            raise CommandError(f"Command failed: {str(e)}")

    def create_user(self, options: Dict) -> None:
        """Create a new user account with profile"""
        username = options['username']
        email = options['email']
        role = options['role']
        department = options['department']
        
        # Check if user already exists
        if User.objects.filter(username=username).exists():
            raise CommandError(f"User '{username}' already exists")
        
        if User.objects.filter(email=email).exists():
            raise CommandError(f"User with email '{email}' already exists")
        
        # Generate password if not provided
        password = options.get('password') or self.generate_password()
        
        try:
            with transaction.atomic():
                # Create user
                user = User.objects.create_user(
                    username=username,
                    email=email,
                    password=password,
                    first_name=options.get('first_name', ''),
                    last_name=options.get('last_name', '')
                )
                
                # Create user profile
                profile = UserProfile.objects.create(
                    user=user,
                    role=role,
                    department=department,
                    phone_number=options.get('phone') or '',  # Ensure empty string, not None
                    is_active=True
                )
                
                self.stdout.write(
                    self.style.SUCCESS(
                        f"Successfully created user '{username}' with role '{role}' in department '{department}'"
                    )
                )
                
                # Send welcome email if requested
                if options.get('send_email'):
                    self.send_welcome_email(user, password)
                    
        except Exception as e:
            raise CommandError(f"Failed to create user: {str(e)}")

    def generate_password(self, length: int = 12) -> str:
        """Generate a secure random password"""
        alphabet = string.ascii_letters + string.digits + "!@#$%^&*"
        password = ''.join(secrets.choice(alphabet) for _ in range(length))
        return password

    def update_user(self, options: Dict) -> None:
        """Update an existing user account"""
        username = options['username']

        try:
            user = User.objects.get(username=username)
            profile = user.userprofile
        except User.DoesNotExist:
            raise CommandError(f"User '{username}' does not exist")
        except UserProfile.DoesNotExist:
            raise CommandError(f"User profile for '{username}' does not exist")

        try:
            with transaction.atomic():
                # Update user fields
                if options.get('email'):
                    if User.objects.filter(email=options['email']).exclude(id=user.id).exists():
                        raise CommandError(f"Email '{options['email']}' is already in use")
                    user.email = options['email']

                if options.get('first_name') is not None:
                    user.first_name = options['first_name']

                if options.get('last_name') is not None:
                    user.last_name = options['last_name']

                if options.get('active') is not None:
                    user.is_active = options['active']

                user.save()

                # Update profile fields
                if options.get('role'):
                    profile.role = options['role']

                if options.get('department'):
                    profile.department = options['department']

                if options.get('phone') is not None:
                    profile.phone_number = options['phone']

                profile.save()

                self.stdout.write(
                    self.style.SUCCESS(f"Successfully updated user '{username}'")
                )

        except Exception as e:
            raise CommandError(f"Failed to update user: {str(e)}")

    def delete_user(self, options: Dict) -> None:
        """Delete a user account"""
        username = options['username']

        try:
            user = User.objects.get(username=username)
        except User.DoesNotExist:
            raise CommandError(f"User '{username}' does not exist")

        # Check for related supply requests
        request_count = SupplyRequest.objects.filter(requester=user).count()
        if request_count > 0:
            self.stdout.write(
                self.style.WARNING(
                    f"User '{username}' has {request_count} associated supply requests"
                )
            )

        try:
            with transaction.atomic():
                user.delete()
                self.stdout.write(
                    self.style.SUCCESS(f"Successfully deleted user '{username}'")
                )
        except Exception as e:
            raise CommandError(f"Failed to delete user: {str(e)}")

    def bulk_import_users(self, options: Dict) -> None:
        """Bulk import users from CSV file"""
        file_path = options['file']

        if not os.path.exists(file_path):
            raise CommandError(f"File '{file_path}' does not exist")

        users_data = []
        errors = []

        try:
            with open(file_path, 'r', newline='', encoding='utf-8') as csvfile:
                reader = csv.DictReader(csvfile)

                # Validate CSV headers
                required_fields = ['username', 'email', 'role', 'department']
                missing_fields = [field for field in required_fields if field not in reader.fieldnames]
                if missing_fields:
                    raise CommandError(f"Missing required CSV columns: {', '.join(missing_fields)}")

                for row_num, row in enumerate(reader, start=2):
                    try:
                        # Validate required fields
                        for field in required_fields:
                            if not row.get(field, '').strip():
                                errors.append(f"Row {row_num}: Missing {field}")
                                continue

                        # Validate role
                        if row['role'] not in ['admin', 'gso_staff', 'department_user']:
                            errors.append(f"Row {row_num}: Invalid role '{row['role']}'")
                            continue

                        # Check for duplicate usernames/emails
                        if User.objects.filter(username=row['username']).exists():
                            errors.append(f"Row {row_num}: Username '{row['username']}' already exists")
                            continue

                        if User.objects.filter(email=row['email']).exists():
                            errors.append(f"Row {row_num}: Email '{row['email']}' already exists")
                            continue

                        users_data.append({
                            'username': row['username'].strip(),
                            'email': row['email'].strip(),
                            'first_name': row.get('first_name', '').strip(),
                            'last_name': row.get('last_name', '').strip(),
                            'role': row['role'].strip(),
                            'department': row['department'].strip(),
                            'phone': row.get('phone', '').strip(),
                            'password': row.get('password', '').strip() or self.generate_password()
                        })

                    except Exception as e:
                        errors.append(f"Row {row_num}: {str(e)}")

        except Exception as e:
            raise CommandError(f"Failed to read CSV file: {str(e)}")

        # Report validation errors
        if errors:
            self.stdout.write(self.style.ERROR("Validation errors found:"))
            for error in errors:
                self.stdout.write(self.style.ERROR(f"  {error}"))

            if options.get('dry_run'):
                return

            raise CommandError("Fix validation errors before importing")

        if options.get('dry_run'):
            self.stdout.write(
                self.style.SUCCESS(f"Validation successful. {len(users_data)} users ready for import.")
            )
            return

        # Import users
        created_users = []
        try:
            with transaction.atomic():
                for user_data in users_data:
                    user = User.objects.create_user(
                        username=user_data['username'],
                        email=user_data['email'],
                        password=user_data['password'],
                        first_name=user_data['first_name'],
                        last_name=user_data['last_name']
                    )

                    UserProfile.objects.create(
                        user=user,
                        role=user_data['role'],
                        department=user_data['department'],
                        phone_number=user_data['phone'],
                        is_active=True
                    )

                    created_users.append((user, user_data['password']))

                self.stdout.write(
                    self.style.SUCCESS(f"Successfully imported {len(created_users)} users")
                )

                # Send welcome emails if requested
                if options.get('send_emails'):
                    for user, password in created_users:
                        try:
                            self.send_welcome_email(user, password)
                        except Exception as e:
                            self.stdout.write(
                                self.style.WARNING(f"Failed to send email to {user.email}: {str(e)}")
                            )

        except Exception as e:
            raise CommandError(f"Failed to import users: {str(e)}")

    def reset_password(self, options: Dict) -> None:
        """Reset user password"""
        username = options['username']

        try:
            user = User.objects.get(username=username)
        except User.DoesNotExist:
            raise CommandError(f"User '{username}' does not exist")

        password = options.get('password') or self.generate_password()

        try:
            user.set_password(password)
            user.save()

            self.stdout.write(
                self.style.SUCCESS(f"Successfully reset password for user '{username}'")
            )

            if options.get('send_email'):
                self.send_password_reset_email(user, password)

        except Exception as e:
            raise CommandError(f"Failed to reset password: {str(e)}")

    def generate_report(self, options: Dict) -> None:
        """Generate user activity report"""
        days = options.get('days', 30)
        role_filter = options.get('role')
        department_filter = options.get('department')
        output_format = options.get('format', 'csv')
        output_file = options.get('output')

        # Calculate date range
        end_date = timezone.now()
        start_date = end_date - timedelta(days=days)

        # Build user queryset
        users = User.objects.select_related('userprofile').all()

        if role_filter:
            users = users.filter(userprofile__role=role_filter)

        if department_filter:
            users = users.filter(userprofile__department=department_filter)

        # Collect user data with activity
        user_data = []
        for user in users:
            try:
                profile = user.userprofile

                # Get supply request activity
                requests_count = SupplyRequest.objects.filter(
                    requester=user,
                    requested_date__gte=start_date
                ).count()

                approved_count = SupplyRequest.objects.filter(
                    approved_by=user,
                    approved_date__gte=start_date
                ).count()

                user_data.append({
                    'username': user.username,
                    'email': user.email,
                    'first_name': user.first_name,
                    'last_name': user.last_name,
                    'role': profile.role,
                    'department': profile.department,
                    'phone': profile.phone_number,
                    'is_active': user.is_active,
                    'date_joined': user.date_joined.strftime('%Y-%m-%d'),
                    'last_login': user.last_login.strftime('%Y-%m-%d %H:%M') if user.last_login else 'Never',
                    'requests_submitted': requests_count,
                    'requests_approved': approved_count
                })

            except UserProfile.DoesNotExist:
                # Skip users without profiles
                continue

        # Output report
        if output_format == 'csv':
            self.output_csv_report(user_data, output_file)
        else:
            self.output_json_report(user_data, output_file)

    def list_users(self, options: Dict) -> None:
        """List users with filtering options"""
        role_filter = options.get('role')
        department_filter = options.get('department')
        active_filter = options.get('active')
        output_format = options.get('format', 'table')

        # Build queryset
        users = User.objects.select_related('userprofile').all()

        if role_filter:
            users = users.filter(userprofile__role=role_filter)

        if department_filter:
            users = users.filter(userprofile__department=department_filter)

        if active_filter is not None:
            users = users.filter(is_active=active_filter)

        if output_format == 'table':
            self.output_user_table(users)
        else:
            self.output_user_csv(users)

    def send_welcome_email(self, user: User, password: str) -> None:
        """Send welcome email to new user"""
        subject = 'Welcome to SmartSupply System'
        message = f"""
        Welcome to the SmartSupply System!

        Your account has been created with the following credentials:
        Username: {user.username}
        Password: {password}

        Please log in and change your password at your earliest convenience.

        Best regards,
        SmartSupply Team
        """

        try:
            send_mail(
                subject,
                message,
                settings.DEFAULT_FROM_EMAIL,
                [user.email],
                fail_silently=False
            )
            self.stdout.write(
                self.style.SUCCESS(f"Welcome email sent to {user.email}")
            )
        except Exception as e:
            self.stdout.write(
                self.style.WARNING(f"Failed to send welcome email to {user.email}: {str(e)}")
            )

    def send_password_reset_email(self, user: User, password: str) -> None:
        """Send password reset email"""
        subject = 'SmartSupply Password Reset'
        message = f"""
        Your SmartSupply password has been reset.

        Username: {user.username}
        New Password: {password}

        Please log in and change your password immediately.

        Best regards,
        SmartSupply Team
        """

        try:
            send_mail(
                subject,
                message,
                settings.DEFAULT_FROM_EMAIL,
                [user.email],
                fail_silently=False
            )
            self.stdout.write(
                self.style.SUCCESS(f"Password reset email sent to {user.email}")
            )
        except Exception as e:
            self.stdout.write(
                self.style.WARNING(f"Failed to send password reset email to {user.email}: {str(e)}")
            )

    def output_csv_report(self, user_data: List[Dict], output_file: Optional[str]) -> None:
        """Output user report in CSV format"""
        import io

        if not user_data:
            self.stdout.write(self.style.WARNING("No users found matching criteria"))
            return

        output = io.StringIO()
        fieldnames = user_data[0].keys()
        writer = csv.DictWriter(output, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(user_data)

        content = output.getvalue()

        if output_file:
            with open(output_file, 'w', newline='', encoding='utf-8') as f:
                f.write(content)
            self.stdout.write(
                self.style.SUCCESS(f"Report saved to {output_file}")
            )
        else:
            self.stdout.write(content)

    def output_json_report(self, user_data: List[Dict], output_file: Optional[str]) -> None:
        """Output user report in JSON format"""
        import json

        if not user_data:
            self.stdout.write(self.style.WARNING("No users found matching criteria"))
            return

        content = json.dumps(user_data, indent=2, default=str)

        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(content)
            self.stdout.write(
                self.style.SUCCESS(f"Report saved to {output_file}")
            )
        else:
            self.stdout.write(content)

    def output_user_table(self, users) -> None:
        """Output users in table format"""
        if not users.exists():
            self.stdout.write(self.style.WARNING("No users found matching criteria"))
            return

        # Table headers
        headers = ['Username', 'Email', 'Name', 'Role', 'Department', 'Active', 'Last Login']

        # Calculate column widths
        col_widths = [len(header) for header in headers]

        rows = []
        for user in users:
            try:
                profile = user.userprofile
                row = [
                    user.username,
                    user.email,
                    f"{user.first_name} {user.last_name}".strip() or '-',
                    profile.role,
                    profile.department,
                    'Yes' if user.is_active else 'No',
                    user.last_login.strftime('%Y-%m-%d') if user.last_login else 'Never'
                ]
                rows.append(row)

                # Update column widths
                for i, cell in enumerate(row):
                    col_widths[i] = max(col_widths[i], len(str(cell)))

            except UserProfile.DoesNotExist:
                continue

        # Print table
        separator = '+' + '+'.join('-' * (width + 2) for width in col_widths) + '+'

        self.stdout.write(separator)

        # Headers
        header_row = '|' + '|'.join(f" {header:<{col_widths[i]}} " for i, header in enumerate(headers)) + '|'
        self.stdout.write(header_row)
        self.stdout.write(separator)

        # Data rows
        for row in rows:
            data_row = '|' + '|'.join(f" {str(cell):<{col_widths[i]}} " for i, cell in enumerate(row)) + '|'
            self.stdout.write(data_row)

        self.stdout.write(separator)
        self.stdout.write(f"\nTotal users: {len(rows)}")

    def output_user_csv(self, users) -> None:
        """Output users in CSV format"""
        if not users.exists():
            self.stdout.write(self.style.WARNING("No users found matching criteria"))
            return

        import io

        output = io.StringIO()
        fieldnames = ['username', 'email', 'first_name', 'last_name', 'role', 'department', 'phone', 'is_active', 'last_login']
        writer = csv.DictWriter(output, fieldnames=fieldnames)
        writer.writeheader()

        for user in users:
            try:
                profile = user.userprofile
                writer.writerow({
                    'username': user.username,
                    'email': user.email,
                    'first_name': user.first_name,
                    'last_name': user.last_name,
                    'role': profile.role,
                    'department': profile.department,
                    'phone': profile.phone_number,
                    'is_active': user.is_active,
                    'last_login': user.last_login.strftime('%Y-%m-%d %H:%M') if user.last_login else 'Never'
                })
            except UserProfile.DoesNotExist:
                continue

        self.stdout.write(output.getvalue())
