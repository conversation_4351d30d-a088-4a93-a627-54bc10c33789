"""
Reporting services for generating comprehensive reports in PDF and CSV formats.
Handles supply request reports, inventory reports, approval workflow reports, and more.
"""

import csv
import io
from datetime import datetime, timedelta
from django.http import HttpResponse
from django.template.loader import render_to_string
from django.utils import timezone
from django.db.models import Count, Sum, Avg, Q, F, Case, When, IntegerField
from django.contrib.auth.models import User
from reportlab.lib import colors
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, PageBreak
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
# WeasyPrint imports commented out due to Windows compatibility issues
# from weasyprint import HTML, CSS
# from weasyprint.text.fonts import FontConfiguration

from .models import SupplyRequest, RequestItem, SupplyItem, StockTransaction, QRCode, ScanActivity, Category


class ReportGenerator:
    """Main class for generating various types of reports"""
    
    def __init__(self):
        self.styles = getSampleStyleSheet()
        self.title_style = ParagraphStyle(
            'CustomTitle',
            parent=self.styles['Heading1'],
            fontSize=16,
            spaceAfter=30,
            alignment=1  # Center alignment
        )
        self.heading_style = ParagraphStyle(
            'CustomHeading',
            parent=self.styles['Heading2'],
            fontSize=12,
            spaceAfter=12
        )
    
    def generate_supply_request_report(self, date_from=None, date_to=None, status=None, department=None, format='pdf'):
        """Generate comprehensive supply request report"""
        # Build query
        requests = SupplyRequest.objects.select_related(
            'requester', 'approved_by'
        ).prefetch_related(
            'request_items__supply_item__category'
        )
        
        # Apply filters
        if date_from:
            requests = requests.filter(requested_date__date__gte=date_from)
        if date_to:
            requests = requests.filter(requested_date__date__lte=date_to)
        if status:
            requests = requests.filter(status=status)
        if department:
            requests = requests.filter(department__icontains=department)
        
        requests = requests.order_by('-requested_date')
        
        # Calculate statistics
        stats = self._calculate_request_statistics(requests, date_from, date_to)
        
        if format == 'csv':
            return self._generate_request_csv(requests, stats)
        else:
            return self._generate_request_pdf(requests, stats, date_from, date_to)
    
    def generate_inventory_report(self, category=None, low_stock_only=False, format='pdf'):
        """Generate inventory report with stock levels and transaction history"""
        # Build query
        items = SupplyItem.objects.select_related('category').prefetch_related(
            'transactions__performed_by'
        )
        
        # Apply filters
        if category:
            items = items.filter(category=category)
        if low_stock_only:
            items = items.filter(current_stock__lte=F('minimum_threshold'))
        
        items = items.filter(is_active=True).order_by('category__name', 'name')
        
        # Calculate statistics
        stats = self._calculate_inventory_statistics(items)
        
        if format == 'csv':
            return self._generate_inventory_csv(items, stats)
        else:
            return self._generate_inventory_pdf(items, stats)
    
    def generate_approval_workflow_report(self, date_from=None, date_to=None, format='pdf'):
        """Generate approval workflow report showing processing times and patterns"""
        # Get requests with approval data
        requests = SupplyRequest.objects.exclude(status='pending').select_related(
            'requester', 'approved_by'
        )
        
        # Apply date filters
        if date_from:
            requests = requests.filter(requested_date__date__gte=date_from)
        if date_to:
            requests = requests.filter(requested_date__date__lte=date_to)
        
        requests = requests.order_by('-requested_date')
        
        # Calculate workflow statistics
        stats = self._calculate_workflow_statistics(requests)
        
        if format == 'csv':
            return self._generate_workflow_csv(requests, stats)
        else:
            return self._generate_workflow_pdf(requests, stats, date_from, date_to)
    
    def _calculate_request_statistics(self, requests, date_from=None, date_to=None):
        """Calculate statistics for supply requests"""
        total_requests = requests.count()
        
        # Status breakdown
        status_counts = requests.values('status').annotate(count=Count('id'))
        status_stats = {item['status']: item['count'] for item in status_counts}
        
        # Department breakdown
        dept_counts = requests.values('department').annotate(count=Count('id')).order_by('-count')[:10]
        
        # Monthly trends (if date range spans multiple months)
        monthly_trends = []
        if date_from and date_to:
            monthly_data = requests.extra(
                select={'month': "strftime('%%Y-%%m', requested_date)"}
            ).values('month').annotate(count=Count('id')).order_by('month')
            monthly_trends = list(monthly_data)
        
        # Cost analysis
        total_cost = 0
        approved_cost = 0
        for request in requests:
            total_cost += request.total_cost
            if request.status in ['approved', 'released', 'completed']:
                approved_items = request.request_items.filter(quantity_approved__gt=0)
                approved_cost += sum(item.approved_total_cost for item in approved_items)
        
        return {
            'total_requests': total_requests,
            'status_stats': status_stats,
            'department_stats': list(dept_counts),
            'monthly_trends': monthly_trends,
            'total_cost': total_cost,
            'approved_cost': approved_cost,
            'date_range': f"{date_from or 'All'} to {date_to or 'All'}"
        }
    
    def _calculate_inventory_statistics(self, items):
        """Calculate inventory statistics"""
        total_items = items.count()
        total_value = sum(item.total_value for item in items)
        low_stock_items = items.filter(current_stock__lte=F('minimum_threshold')).count()
        out_of_stock_items = items.filter(current_stock=0).count()
        
        # Category breakdown
        category_stats = items.values('category__name').annotate(
            count=Count('id'),
            total_stock=Sum('current_stock'),
            total_value=Sum(F('current_stock') * F('unit_cost'))
        ).order_by('-total_value')
        
        return {
            'total_items': total_items,
            'total_value': total_value,
            'low_stock_items': low_stock_items,
            'out_of_stock_items': out_of_stock_items,
            'category_stats': list(category_stats)
        }
    
    def _calculate_workflow_statistics(self, requests):
        """Calculate workflow processing statistics"""
        total_processed = requests.count()
        
        # Status breakdown
        status_counts = requests.values('status').annotate(count=Count('id'))
        status_stats = {item['status']: item['count'] for item in status_counts}
        
        # Processing time analysis
        approved_requests = requests.filter(status__in=['approved', 'released', 'completed'], approved_date__isnull=False)
        processing_times = []
        
        for request in approved_requests:
            if request.approved_date:
                processing_time = (request.approved_date - request.requested_date).days
                processing_times.append(processing_time)
        
        avg_processing_time = sum(processing_times) / len(processing_times) if processing_times else 0
        
        # Approval rate
        approval_rate = (status_stats.get('approved', 0) + status_stats.get('released', 0) + status_stats.get('completed', 0)) / total_processed * 100 if total_processed > 0 else 0
        
        # Top approvers
        approver_stats = requests.filter(approved_by__isnull=False).values(
            'approved_by__first_name', 'approved_by__last_name'
        ).annotate(count=Count('id')).order_by('-count')[:5]
        
        return {
            'total_processed': total_processed,
            'status_stats': status_stats,
            'avg_processing_time': round(avg_processing_time, 1),
            'approval_rate': round(approval_rate, 1),
            'approver_stats': list(approver_stats),
            'processing_times': processing_times
        }
    
    def _generate_request_pdf(self, requests, stats, date_from, date_to):
        """Generate PDF report for supply requests"""
        buffer = io.BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=A4, rightMargin=72, leftMargin=72, topMargin=72, bottomMargin=18)
        
        # Container for the 'Flowable' objects
        elements = []
        
        # Title
        title = Paragraph("Supply Request Report", self.title_style)
        elements.append(title)
        elements.append(Spacer(1, 12))
        
        # Report metadata
        report_info = [
            ['Report Generated:', timezone.now().strftime('%Y-%m-%d %H:%M:%S')],
            ['Date Range:', stats['date_range']],
            ['Total Requests:', str(stats['total_requests'])],
        ]
        
        info_table = Table(report_info, colWidths=[2*inch, 3*inch])
        info_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ]))
        elements.append(info_table)
        elements.append(Spacer(1, 20))
        
        # Summary statistics
        elements.append(Paragraph("Summary Statistics", self.heading_style))
        
        summary_data = [['Status', 'Count', 'Percentage']]
        for status, count in stats['status_stats'].items():
            percentage = (count / stats['total_requests'] * 100) if stats['total_requests'] > 0 else 0
            summary_data.append([status.title(), str(count), f"{percentage:.1f}%"])
        
        summary_table = Table(summary_data, colWidths=[2*inch, 1*inch, 1*inch])
        summary_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        elements.append(summary_table)
        elements.append(Spacer(1, 20))
        
        # Department breakdown
        if stats['department_stats']:
            elements.append(Paragraph("Top Departments by Request Volume", self.heading_style))
            
            dept_data = [['Department', 'Requests']]
            for dept in stats['department_stats'][:10]:
                dept_data.append([dept['department'], str(dept['count'])])
            
            dept_table = Table(dept_data, colWidths=[3*inch, 1*inch])
            dept_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('ALIGN', (1, 0), (1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            elements.append(dept_table)
            elements.append(Spacer(1, 20))
        
        # Detailed request list
        elements.append(Paragraph("Detailed Request List", self.heading_style))
        
        request_data = [['Request ID', 'Requester', 'Department', 'Status', 'Date', 'Items', 'Total Cost']]
        for request in requests[:50]:  # Limit to first 50 requests
            request_data.append([
                request.request_id,
                f"{request.requester.first_name} {request.requester.last_name}",
                request.department,
                request.status.title(),
                request.requested_date.strftime('%Y-%m-%d'),
                str(request.total_items),
                f"₱{request.total_cost:.2f}"
            ])
        
        request_table = Table(request_data, colWidths=[1.2*inch, 1.2*inch, 1*inch, 0.8*inch, 0.8*inch, 0.5*inch, 0.8*inch])
        request_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 8),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 4),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        elements.append(request_table)
        
        # Build PDF
        doc.build(elements)
        buffer.seek(0)
        
        response = HttpResponse(buffer.getvalue(), content_type='application/pdf')
        response['Content-Disposition'] = f'attachment; filename="supply_request_report_{timezone.now().strftime("%Y%m%d_%H%M%S")}.pdf"'
        return response
    
    def _generate_request_csv(self, requests, stats):
        """Generate CSV report for supply requests"""
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename="supply_request_report_{timezone.now().strftime("%Y%m%d_%H%M%S")}.csv"'
        
        writer = csv.writer(response)
        
        # Write header information
        writer.writerow(['Supply Request Report'])
        writer.writerow(['Generated:', timezone.now().strftime('%Y-%m-%d %H:%M:%S')])
        writer.writerow(['Date Range:', stats['date_range']])
        writer.writerow(['Total Requests:', stats['total_requests']])
        writer.writerow([])
        
        # Write detailed data
        writer.writerow([
            'Request ID', 'Requester', 'Department', 'Status', 'Requested Date', 
            'Required Date', 'Total Items', 'Total Cost', 'Approved By', 'Approved Date', 'Justification'
        ])
        
        for request in requests:
            writer.writerow([
                request.request_id,
                f"{request.requester.first_name} {request.requester.last_name}",
                request.department,
                request.status,
                request.requested_date.strftime('%Y-%m-%d %H:%M:%S'),
                request.required_date.strftime('%Y-%m-%d'),
                request.total_items,
                request.total_cost,
                f"{request.approved_by.first_name} {request.approved_by.last_name}" if request.approved_by else '',
                request.approved_date.strftime('%Y-%m-%d %H:%M:%S') if request.approved_date else '',
                request.justification
            ])
        
        return response
    
    def _generate_inventory_pdf(self, items, stats):
        """Generate PDF report for inventory"""
        buffer = io.BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=A4, rightMargin=72, leftMargin=72, topMargin=72, bottomMargin=18)
        
        elements = []
        
        # Title
        title = Paragraph("Inventory Report", self.title_style)
        elements.append(title)
        elements.append(Spacer(1, 12))
        
        # Report metadata
        report_info = [
            ['Report Generated:', timezone.now().strftime('%Y-%m-%d %H:%M:%S')],
            ['Total Items:', str(stats['total_items'])],
            ['Total Value:', f"₱{stats['total_value']:.2f}"],
            ['Low Stock Items:', str(stats['low_stock_items'])],
            ['Out of Stock Items:', str(stats['out_of_stock_items'])],
        ]
        
        info_table = Table(report_info, colWidths=[2*inch, 3*inch])
        info_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ]))
        elements.append(info_table)
        elements.append(Spacer(1, 20))
        
        # Category breakdown
        elements.append(Paragraph("Inventory by Category", self.heading_style))
        
        category_data = [['Category', 'Items', 'Total Stock', 'Total Value']]
        for cat in stats['category_stats']:
            category_data.append([
                cat['category__name'],
                str(cat['count']),
                str(cat['total_stock']),
                f"₱{cat['total_value']:.2f}"
            ])
        
        category_table = Table(category_data, colWidths=[2*inch, 1*inch, 1*inch, 1.5*inch])
        category_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        elements.append(category_table)
        elements.append(Spacer(1, 20))
        
        # Detailed inventory list
        elements.append(Paragraph("Detailed Inventory List", self.heading_style))
        
        inventory_data = [['Item Code', 'Name', 'Category', 'Current Stock', 'Min Threshold', 'Unit Cost', 'Total Value', 'Status']]
        for item in items:
            status = 'Out of Stock' if item.current_stock == 0 else ('Low Stock' if item.is_low_stock else 'In Stock')
            inventory_data.append([
                item.item_code,
                item.name[:30] + '...' if len(item.name) > 30 else item.name,
                item.category.name,
                str(item.current_stock),
                str(item.minimum_threshold),
                f"₱{item.unit_cost:.2f}",
                f"₱{item.total_value:.2f}",
                status
            ])
        
        inventory_table = Table(inventory_data, colWidths=[0.8*inch, 1.5*inch, 0.8*inch, 0.6*inch, 0.6*inch, 0.7*inch, 0.8*inch, 0.7*inch])
        inventory_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 8),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 4),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        elements.append(inventory_table)
        
        # Build PDF
        doc.build(elements)
        buffer.seek(0)
        
        response = HttpResponse(buffer.getvalue(), content_type='application/pdf')
        response['Content-Disposition'] = f'attachment; filename="inventory_report_{timezone.now().strftime("%Y%m%d_%H%M%S")}.pdf"'
        return response
    
    def _generate_inventory_csv(self, items, stats):
        """Generate CSV report for inventory"""
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename="inventory_report_{timezone.now().strftime("%Y%m%d_%H%M%S")}.csv"'
        
        writer = csv.writer(response)
        
        # Write header information
        writer.writerow(['Inventory Report'])
        writer.writerow(['Generated:', timezone.now().strftime('%Y-%m-%d %H:%M:%S')])
        writer.writerow(['Total Items:', stats['total_items']])
        writer.writerow(['Total Value:', f"₱{stats['total_value']:.2f}"])
        writer.writerow(['Low Stock Items:', stats['low_stock_items']])
        writer.writerow(['Out of Stock Items:', stats['out_of_stock_items']])
        writer.writerow([])
        
        # Write detailed data
        writer.writerow([
            'Item Code', 'Name', 'Description', 'Category', 'Unit of Measure',
            'Current Stock', 'Minimum Threshold', 'Unit Cost', 'Total Value',
            'Supplier', 'Status', 'Last Updated'
        ])
        
        for item in items:
            status = 'Out of Stock' if item.current_stock == 0 else ('Low Stock' if item.is_low_stock else 'In Stock')
            writer.writerow([
                item.item_code,
                item.name,
                item.description,
                item.category.name,
                item.unit_of_measure,
                item.current_stock,
                item.minimum_threshold,
                item.unit_cost,
                item.total_value,
                item.supplier,
                status,
                item.last_updated.strftime('%Y-%m-%d %H:%M:%S')
            ])
        
        return response
    
    def _generate_workflow_pdf(self, requests, stats, date_from, date_to):
        """Generate PDF report for approval workflow"""
        buffer = io.BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=A4, rightMargin=72, leftMargin=72, topMargin=72, bottomMargin=18)
        
        elements = []
        
        # Title
        title = Paragraph("Approval Workflow Report", self.title_style)
        elements.append(title)
        elements.append(Spacer(1, 12))
        
        # Report metadata
        date_range = f"{date_from or 'All'} to {date_to or 'All'}"
        report_info = [
            ['Report Generated:', timezone.now().strftime('%Y-%m-%d %H:%M:%S')],
            ['Date Range:', date_range],
            ['Total Processed:', str(stats['total_processed'])],
            ['Average Processing Time:', f"{stats['avg_processing_time']} days"],
            ['Approval Rate:', f"{stats['approval_rate']}%"],
        ]
        
        info_table = Table(report_info, colWidths=[2*inch, 3*inch])
        info_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ]))
        elements.append(info_table)
        elements.append(Spacer(1, 20))
        
        # Status breakdown
        elements.append(Paragraph("Processing Status Breakdown", self.heading_style))
        
        status_data = [['Status', 'Count', 'Percentage']]
        for status, count in stats['status_stats'].items():
            percentage = (count / stats['total_processed'] * 100) if stats['total_processed'] > 0 else 0
            status_data.append([status.title(), str(count), f"{percentage:.1f}%"])
        
        status_table = Table(status_data, colWidths=[2*inch, 1*inch, 1*inch])
        status_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        elements.append(status_table)
        elements.append(Spacer(1, 20))
        
        # Top approvers
        if stats['approver_stats']:
            elements.append(Paragraph("Top Approvers", self.heading_style))
            
            approver_data = [['Approver', 'Requests Processed']]
            for approver in stats['approver_stats']:
                name = f"{approver['approved_by__first_name']} {approver['approved_by__last_name']}"
                approver_data.append([name, str(approver['count'])])
            
            approver_table = Table(approver_data, colWidths=[3*inch, 1.5*inch])
            approver_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('ALIGN', (1, 0), (1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            elements.append(approver_table)
            elements.append(Spacer(1, 20))
        
        # Build PDF
        doc.build(elements)
        buffer.seek(0)
        
        response = HttpResponse(buffer.getvalue(), content_type='application/pdf')
        response['Content-Disposition'] = f'attachment; filename="workflow_report_{timezone.now().strftime("%Y%m%d_%H%M%S")}.pdf"'
        return response
    
    def _generate_workflow_csv(self, requests, stats):
        """Generate CSV report for approval workflow"""
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename="workflow_report_{timezone.now().strftime("%Y%m%d_%H%M%S")}.csv"'
        
        writer = csv.writer(response)
        
        # Write header information
        writer.writerow(['Approval Workflow Report'])
        writer.writerow(['Generated:', timezone.now().strftime('%Y-%m-%d %H:%M:%S')])
        writer.writerow(['Total Processed:', stats['total_processed']])
        writer.writerow(['Average Processing Time:', f"{stats['avg_processing_time']} days"])
        writer.writerow(['Approval Rate:', f"{stats['approval_rate']}%"])
        writer.writerow([])
        
        # Write detailed data
        writer.writerow([
            'Request ID', 'Requester', 'Department', 'Status', 'Requested Date',
            'Approved Date', 'Processing Time (Days)', 'Approved By', 'Total Cost'
        ])
        
        for request in requests:
            processing_time = ''
            if request.approved_date:
                processing_time = (request.approved_date - request.requested_date).days
            
            writer.writerow([
                request.request_id,
                f"{request.requester.first_name} {request.requester.last_name}",
                request.department,
                request.status,
                request.requested_date.strftime('%Y-%m-%d %H:%M:%S'),
                request.approved_date.strftime('%Y-%m-%d %H:%M:%S') if request.approved_date else '',
                processing_time,
                f"{request.approved_by.first_name} {request.approved_by.last_name}" if request.approved_by else '',
                request.total_cost
            ])
        
        return response


class ScheduledReportGenerator:
    """Class for handling automated report generation scheduling"""
    
    @staticmethod
    def generate_daily_summary():
        """Generate daily summary report"""
        today = timezone.now().date()
        yesterday = today - timedelta(days=1)
        
        generator = ReportGenerator()
        return generator.generate_supply_request_report(
            date_from=yesterday,
            date_to=today,
            format='pdf'
        )
    
    @staticmethod
    def generate_weekly_inventory_report():
        """Generate weekly inventory report"""
        generator = ReportGenerator()
        return generator.generate_inventory_report(
            low_stock_only=True,
            format='pdf'
        )
    
    @staticmethod
    def generate_monthly_workflow_report():
        """Generate monthly workflow analysis report"""
        today = timezone.now().date()
        month_start = today.replace(day=1)
        
        generator = ReportGenerator()
        return generator.generate_approval_workflow_report(
            date_from=month_start,
            date_to=today,
            format='pdf'
        )
