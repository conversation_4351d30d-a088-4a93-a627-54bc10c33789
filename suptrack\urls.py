from django.urls import path
from . import views
from django.contrib.auth import views as auth_views

app_name = 'suptrack'

urlpatterns = [
    path('', views.dashboard, name='dashboard'),
    path('password_reset/', auth_views.PasswordResetView.as_view(template_name='registration/password_reset.html'), name='password_reset'),
    path('password_reset/done/', auth_views.PasswordResetDoneView.as_view(template_name='registration/password_reset_done.html'), name='password_reset_done'),
    path('reset/<uidb64>/<token>/', auth_views.PasswordResetConfirmView.as_view(template_name='registration/password_reset_confirm.html'), name='password_reset_confirm'),
    path('reset/done/', auth_views.PasswordResetCompleteView.as_view(template_name='registration/password_reset_complete.html'), name='password_reset_complete'),
    path('register/', views.register_view, name='register'),
    path('login/', views.login_view, name='login'),
    path('logout/', views.logout_view, name='logout'),
    path('supply-request/new/', views.create_supply_request, name='create_supply_request'),
    path('supply-request/', views.supply_request_list, name='supply_request_list'),
    path('supply-request/<int:pk>/', views.supply_request_detail, name='supply_request_detail'),
    path('supply-request/<int:pk>/review/', views.review_supply_request, name='review_supply_request'),
    path('inventory/', views.inventory_dashboard, name='inventory_dashboard'),
    path('inventory/item/<int:pk>/', views.item_detail, name='item_detail'),
    path('inventory/item/<int:pk>/edit/', views.edit_item, name='edit_item'),
    path('inventory/low-stock-alerts/', views.low_stock_alert, name='low_stock_alert'),
    path('inventory/qr-scanner/', views.qr_scanner, name='qr_scanner'),
    path('inventory/process-scan/<str:item_code>/', views.process_qr_scan, name='process_qr_scan'),
    path('inventory/item/<int:pk>/issue/', views.issue_stock, name='issue_stock'),
    path('inventory/item/<int:pk>/return/', views.return_stock, name='return_stock'),
    path('inventory/audit-scan/<str:item_code>/', views.audit_item, name='audit_item'),
    path('inventory/item/<int:pk>/process-audit/', views.process_audit, name='process_audit'),
]