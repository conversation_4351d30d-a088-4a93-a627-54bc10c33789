"""
Django management command for comprehensive inventory management in SmartSupply.

This command provides functionality for:
- Bulk importing/exporting inventory items from CSV/Excel files
- Updating stock levels with audit trail logging
- Generating low stock alerts and reorder notifications
- Performing inventory reconciliation and stock adjustments
- Creating QR codes for inventory items
- Generating inventory reports (current stock, usage patterns, cost analysis)
- Handling supply item categorization and pricing updates

Usage Examples:
    # Bulk import inventory from CSV
    python manage.py manage_inventory import --file inventory.csv --create_qr

    # Export current inventory
    python manage.py manage_inventory export --file current_inventory.csv --format csv

    # Update stock levels
    python manage.py manage_inventory update_stock --item_code IT001 --quantity 100 --reason "Stock replenishment"

    # Generate low stock report
    python manage.py manage_inventory low_stock --output low_stock.csv --threshold 10

    # Perform inventory reconciliation
    python manage.py manage_inventory reconcile --file physical_count.csv --create_adjustments

    # Generate QR codes for all items
    python manage.py manage_inventory generate_qr --output_dir qr_codes/

    # Generate usage report
    python manage.py manage_inventory usage_report --days 30 --output usage_report.csv

    # Update pricing
    python manage.py manage_inventory update_pricing --file pricing_updates.csv
"""

import csv
import json
import logging
import os
import qrcode
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Tuple
from io import BytesIO

from django.core.exceptions import ValidationError
from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from django.db.models import Q, Sum, Count, Avg, F
from django.db import models
from django.utils import timezone
from django.conf import settings

from suptrack.models import SupplyItem, SupplyRequest, RequestItem, Category

# Configure logging
logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Comprehensive inventory management for SmartSupply application'

    def add_arguments(self, parser):
        """Add command line arguments"""
        subparsers = parser.add_subparsers(dest='action', help='Available actions')

        # Import inventory subcommand
        import_parser = subparsers.add_parser('import', help='Import inventory items from file')
        import_parser.add_argument('--file', required=True, help='Path to CSV/Excel file')
        import_parser.add_argument('--format', choices=['csv', 'excel'], default='csv',
                                 help='File format')
        import_parser.add_argument('--create_qr', action='store_true',
                                 help='Generate QR codes for imported items')
        import_parser.add_argument('--update_existing', action='store_true',
                                 help='Update existing items instead of creating new ones')
        import_parser.add_argument('--dry_run', action='store_true',
                                 help='Validate file without importing')

        # Export inventory subcommand
        export_parser = subparsers.add_parser('export', help='Export inventory items to file')
        export_parser.add_argument('--file', required=True, help='Output file path')
        export_parser.add_argument('--format', choices=['csv', 'excel', 'json'], default='csv',
                                 help='Output format')
        export_parser.add_argument('--category', help='Filter by category')
        export_parser.add_argument('--low_stock_only', action='store_true',
                                 help='Export only low stock items')

        # Update stock subcommand
        stock_parser = subparsers.add_parser('update_stock', help='Update stock levels for items')
        stock_parser.add_argument('--item_code', required=True, help='Item code to update')
        stock_parser.add_argument('--quantity', type=int, required=True, help='New stock quantity')
        stock_parser.add_argument('--reason', required=True, help='Reason for stock update')
        stock_parser.add_argument('--adjustment_type', choices=['set', 'add', 'subtract'], default='set',
                                help='Type of adjustment')

        # Low stock report subcommand
        low_stock_parser = subparsers.add_parser('low_stock', help='Generate low stock report')
        low_stock_parser.add_argument('--output', help='Output file path')
        low_stock_parser.add_argument('--threshold', type=int, help='Custom low stock threshold')
        low_stock_parser.add_argument('--format', choices=['csv', 'json'], default='csv',
                                    help='Output format')

        # Reconciliation subcommand
        reconcile_parser = subparsers.add_parser('reconcile', help='Perform inventory reconciliation')
        reconcile_parser.add_argument('--file', required=True, help='Physical count CSV file')
        reconcile_parser.add_argument('--create_adjustments', action='store_true',
                                    help='Create stock adjustments for discrepancies')
        reconcile_parser.add_argument('--tolerance', type=int, default=0,
                                    help='Tolerance for discrepancies (default: 0)')

        # Generate QR codes subcommand
        qr_parser = subparsers.add_parser('generate_qr', help='Generate QR codes for inventory items')
        qr_parser.add_argument('--output_dir', required=True, help='Output directory for QR codes')
        qr_parser.add_argument('--item_codes', nargs='*', help='Specific item codes (all if not specified)')
        qr_parser.add_argument('--size', type=int, default=10, help='QR code size (default: 10)')

        # Usage report subcommand
        usage_parser = subparsers.add_parser('usage_report', help='Generate usage pattern report')
        usage_parser.add_argument('--days', type=int, default=30, help='Number of days to analyze')
        usage_parser.add_argument('--output', help='Output file path')
        usage_parser.add_argument('--format', choices=['csv', 'json'], default='csv',
                                help='Output format')
        usage_parser.add_argument('--top_items', type=int, default=50,
                                help='Number of top items to include')

        # Update pricing subcommand
        pricing_parser = subparsers.add_parser('update_pricing', help='Update item pricing')
        pricing_parser.add_argument('--file', required=True, help='CSV file with pricing updates')
        pricing_parser.add_argument('--dry_run', action='store_true',
                                  help='Validate pricing without updating')

        # Inventory report subcommand
        report_parser = subparsers.add_parser('report', help='Generate comprehensive inventory report')
        report_parser.add_argument('--output', help='Output file path')
        report_parser.add_argument('--format', choices=['csv', 'json'], default='csv',
                                 help='Output format')
        report_parser.add_argument('--include_costs', action='store_true',
                                 help='Include cost analysis in report')

        # List items subcommand
        list_parser = subparsers.add_parser('list', help='List inventory items with filtering')
        list_parser.add_argument('--category', help='Filter by category')
        list_parser.add_argument('--low_stock', action='store_true', help='Show only low stock items')
        list_parser.add_argument('--search', help='Search in item names and descriptions')
        list_parser.add_argument('--format', choices=['table', 'csv'], default='table',
                               help='Output format')

    def handle(self, *args, **options):
        """Main command handler"""
        action = options.get('action')
        
        if not action:
            self.print_help('manage.py', 'manage_inventory')
            return

        try:
            if action == 'import':
                self.import_inventory(options)
            elif action == 'export':
                self.export_inventory(options)
            elif action == 'update_stock':
                self.update_stock(options)
            elif action == 'low_stock':
                self.generate_low_stock_report(options)
            elif action == 'reconcile':
                self.reconcile_inventory(options)
            elif action == 'generate_qr':
                self.generate_qr_codes(options)
            elif action == 'usage_report':
                self.generate_usage_report(options)
            elif action == 'update_pricing':
                self.update_pricing(options)
            elif action == 'report':
                self.generate_inventory_report(options)
            elif action == 'list':
                self.list_items(options)
            else:
                raise CommandError(f"Unknown action: {action}")
                
        except Exception as e:
            logger.error(f"Command failed: {str(e)}")
            raise CommandError(f"Command failed: {str(e)}")

    def import_inventory(self, options: Dict) -> None:
        """Import inventory items from CSV/Excel file"""
        file_path = options['file']
        file_format = options['format']
        
        if not os.path.exists(file_path):
            raise CommandError(f"File '{file_path}' does not exist")
        
        items_data = []
        errors = []
        
        try:
            if file_format == 'csv':
                items_data, errors = self.parse_csv_file(file_path)
            else:
                items_data, errors = self.parse_excel_file(file_path)
                
        except Exception as e:
            raise CommandError(f"Failed to parse file: {str(e)}")
        
        # Report validation errors
        if errors:
            self.stdout.write(self.style.ERROR("Validation errors found:"))
            for error in errors:
                self.stdout.write(self.style.ERROR(f"  {error}"))
            
            if options.get('dry_run'):
                return
            
            raise CommandError("Fix validation errors before importing")
        
        if options.get('dry_run'):
            self.stdout.write(
                self.style.SUCCESS(f"Validation successful. {len(items_data)} items ready for import.")
            )
            return
        
        # Import items
        created_count = 0
        updated_count = 0
        
        try:
            with transaction.atomic():
                for item_data in items_data:
                    item_code = item_data['item_code']
                    
                    if options.get('update_existing') and SupplyItem.objects.filter(item_code=item_code).exists():
                        # Update existing item
                        item = SupplyItem.objects.get(item_code=item_code)
                        for field, value in item_data.items():
                            if field != 'item_code' and value is not None:
                                setattr(item, field, value)
                        item.save()
                        updated_count += 1
                    else:
                        # Create new item
                        SupplyItem.objects.create(**item_data)
                        created_count += 1
                
                self.stdout.write(
                    self.style.SUCCESS(
                        f"Successfully imported {created_count} new items and updated {updated_count} existing items"
                    )
                )
                
                # Generate QR codes if requested
                if options.get('create_qr'):
                    self.stdout.write("Generating QR codes for imported items...")
                    # This would be implemented in the generate_qr_codes method
                
        except Exception as e:
            raise CommandError(f"Failed to import items: {str(e)}")

    def parse_csv_file(self, file_path: str) -> Tuple[List[Dict], List[str]]:
        """Parse CSV file and return items data and errors"""
        items_data = []
        errors = []

        required_fields = ['item_code', 'name', 'category', 'unit_of_measure', 'unit_price']

        with open(file_path, 'r', newline='', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)

            # Validate CSV headers
            missing_fields = [field for field in required_fields if field not in reader.fieldnames]
            if missing_fields:
                errors.append(f"Missing required CSV columns: {', '.join(missing_fields)}")
                return items_data, errors

            for row_num, row in enumerate(reader, start=2):
                try:
                    # Validate required fields
                    for field in required_fields:
                        if not row.get(field, '').strip():
                            errors.append(f"Row {row_num}: Missing {field}")
                            continue

                    # Check for duplicate item codes
                    item_code = row['item_code'].strip()
                    if SupplyItem.objects.filter(item_code=item_code).exists():
                        errors.append(f"Row {row_num}: Item code '{item_code}' already exists")
                        continue

                    # Validate and get/create category
                    category_name = row['category'].strip()
                    try:
                        category = Category.objects.get(name=category_name)
                    except Category.DoesNotExist:
                        # Create category if it doesn't exist
                        category = Category.objects.create(
                            name=category_name,
                            description=f"Auto-created category for {category_name}"
                        )

                    # Validate numeric fields
                    try:
                        unit_cost = Decimal(row['unit_price'])  # CSV uses unit_price, model uses unit_cost
                        if unit_cost < 0:
                            errors.append(f"Row {row_num}: Unit price must be non-negative")
                            continue
                    except (ValueError, TypeError):
                        errors.append(f"Row {row_num}: Invalid unit price '{row['unit_price']}'")
                        continue

                    try:
                        current_stock = int(row.get('current_stock', 0))
                        if current_stock < 0:
                            errors.append(f"Row {row_num}: Current stock must be non-negative")
                            continue
                    except (ValueError, TypeError):
                        errors.append(f"Row {row_num}: Invalid current stock '{row.get('current_stock', 0)}'")
                        continue

                    try:
                        reorder_level = int(row.get('reorder_level', 10))
                        if reorder_level < 0:
                            errors.append(f"Row {row_num}: Reorder level must be non-negative")
                            continue
                    except (ValueError, TypeError):
                        errors.append(f"Row {row_num}: Invalid reorder level '{row.get('reorder_level', 10)}'")
                        continue

                    items_data.append({
                        'item_code': item_code,
                        'name': row['name'].strip(),
                        'description': row.get('description', '').strip(),
                        'category': category,
                        'unit_of_measure': row['unit_of_measure'].strip(),
                        'unit_cost': unit_cost,  # Note: field name is unit_cost, not unit_price
                        'current_stock': current_stock,
                        'reorder_level': reorder_level,
                        'supplier': row.get('supplier', '').strip()
                    })

                except Exception as e:
                    errors.append(f"Row {row_num}: {str(e)}")

        return items_data, errors

    def parse_excel_file(self, file_path: str) -> Tuple[List[Dict], List[str]]:
        """Parse Excel file and return items data and errors"""
        try:
            import pandas as pd
        except ImportError:
            raise CommandError("pandas is required for Excel file support. Install with: pip install pandas openpyxl")

        items_data = []
        errors = []

        try:
            df = pd.read_excel(file_path)

            # Convert to CSV-like format for processing
            csv_data = df.to_dict('records')

            # Convert to string format and process like CSV
            for i, row in enumerate(csv_data, start=2):
                # Convert pandas data to string format
                row_str = {k: str(v) if pd.notna(v) else '' for k, v in row.items()}

                # Use existing CSV parsing logic
                # This is a simplified version - in practice, you'd adapt the CSV parsing logic
                pass

        except Exception as e:
            errors.append(f"Failed to parse Excel file: {str(e)}")

        return items_data, errors

    def export_inventory(self, options: Dict) -> None:
        """Export inventory items to file"""
        output_file = options['file']
        output_format = options['format']
        category_filter = options.get('category')
        low_stock_only = options.get('low_stock_only')

        # Build queryset
        items = SupplyItem.objects.all()

        if category_filter:
            items = items.filter(category=category_filter)

        if low_stock_only:
            items = items.filter(current_stock__lte=models.F('reorder_level'))

        if not items.exists():
            self.stdout.write(self.style.WARNING("No items found matching criteria"))
            return

        try:
            if output_format == 'csv':
                self.export_to_csv(items, output_file)
            elif output_format == 'excel':
                self.export_to_excel(items, output_file)
            else:
                self.export_to_json(items, output_file)

            self.stdout.write(
                self.style.SUCCESS(f"Successfully exported {items.count()} items to {output_file}")
            )

        except Exception as e:
            raise CommandError(f"Failed to export inventory: {str(e)}")

    def update_stock(self, options: Dict) -> None:
        """Update stock levels for a specific item"""
        item_code = options['item_code']
        quantity = options['quantity']
        reason = options['reason']
        adjustment_type = options['adjustment_type']

        try:
            item = SupplyItem.objects.get(item_code=item_code)
        except SupplyItem.DoesNotExist:
            raise CommandError(f"Item with code '{item_code}' does not exist")

        old_stock = item.current_stock

        try:
            with transaction.atomic():
                if adjustment_type == 'set':
                    item.current_stock = quantity
                elif adjustment_type == 'add':
                    item.current_stock += quantity
                elif adjustment_type == 'subtract':
                    item.current_stock = max(0, item.current_stock - quantity)

                item.save()

                # Log the stock adjustment (you might want to create a StockAdjustment model)
                self.log_stock_adjustment(item, old_stock, item.current_stock, reason)

                self.stdout.write(
                    self.style.SUCCESS(
                        f"Updated stock for '{item_code}': {old_stock} → {item.current_stock} ({reason})"
                    )
                )

        except Exception as e:
            raise CommandError(f"Failed to update stock: {str(e)}")

    def log_stock_adjustment(self, item: SupplyItem, old_stock: int, new_stock: int, reason: str) -> None:
        """Log stock adjustment for audit trail"""
        # This would typically create a record in a StockAdjustment model
        # For now, we'll just log it
        logger.info(
            f"Stock adjustment: {item.item_code} ({item.name}) "
            f"from {old_stock} to {new_stock}. Reason: {reason}"
        )

    def generate_low_stock_report(self, options: Dict) -> None:
        """Generate low stock alert report"""
        output_file = options.get('output')
        threshold = options.get('threshold')
        output_format = options.get('format', 'csv')

        # Build queryset for low stock items
        if threshold:
            low_stock_items = SupplyItem.objects.filter(current_stock__lte=threshold)
        else:
            low_stock_items = SupplyItem.objects.filter(current_stock__lte=F('reorder_level'))

        if not low_stock_items.exists():
            self.stdout.write(self.style.SUCCESS("No low stock items found"))
            return

        # Prepare report data
        report_data = []
        for item in low_stock_items:
            shortage = max(0, item.reorder_level - item.current_stock)
            report_data.append({
                'item_code': item.item_code,
                'name': item.name,
                'category': item.category.name,
                'current_stock': item.current_stock,
                'reorder_level': item.reorder_level,
                'shortage': shortage,
                'unit_price': str(item.unit_cost),
                'reorder_cost': str(item.unit_cost * shortage),
                'supplier': item.supplier
            })

        # Output report
        if output_format == 'csv':
            self.output_csv_report(report_data, output_file, "Low Stock Report")
        else:
            self.output_json_report(report_data, output_file, "Low Stock Report")

        self.stdout.write(
            self.style.WARNING(f"Found {len(report_data)} items below reorder level")
        )

    def reconcile_inventory(self, options: Dict) -> None:
        """Perform inventory reconciliation against physical count"""
        file_path = options['file']
        create_adjustments = options.get('create_adjustments')
        tolerance = options.get('tolerance', 0)

        if not os.path.exists(file_path):
            raise CommandError(f"File '{file_path}' does not exist")

        discrepancies = []

        try:
            with open(file_path, 'r', newline='', encoding='utf-8') as csvfile:
                reader = csv.DictReader(csvfile)

                if 'item_code' not in reader.fieldnames or 'physical_count' not in reader.fieldnames:
                    raise CommandError("CSV must contain 'item_code' and 'physical_count' columns")

                for row_num, row in enumerate(reader, start=2):
                    item_code = row['item_code'].strip()

                    try:
                        physical_count = int(row['physical_count'])
                    except (ValueError, TypeError):
                        self.stdout.write(
                            self.style.WARNING(f"Row {row_num}: Invalid physical count for {item_code}")
                        )
                        continue

                    try:
                        item = SupplyItem.objects.get(item_code=item_code)
                        system_count = item.current_stock
                        difference = physical_count - system_count

                        if abs(difference) > tolerance:
                            discrepancies.append({
                                'item_code': item_code,
                                'name': item.name,
                                'system_count': system_count,
                                'physical_count': physical_count,
                                'difference': difference,
                                'item': item
                            })

                    except SupplyItem.DoesNotExist:
                        self.stdout.write(
                            self.style.WARNING(f"Row {row_num}: Item '{item_code}' not found in system")
                        )
                        continue

        except Exception as e:
            raise CommandError(f"Failed to process reconciliation file: {str(e)}")

        if not discrepancies:
            self.stdout.write(self.style.SUCCESS("No discrepancies found - inventory is accurate"))
            return

        # Report discrepancies
        self.stdout.write(self.style.WARNING(f"Found {len(discrepancies)} discrepancies:"))
        for disc in discrepancies:
            self.stdout.write(
                f"  {disc['item_code']}: System={disc['system_count']}, "
                f"Physical={disc['physical_count']}, Diff={disc['difference']:+d}"
            )

        # Create adjustments if requested
        if create_adjustments:
            try:
                with transaction.atomic():
                    for disc in discrepancies:
                        item = disc['item']
                        old_stock = item.current_stock
                        item.current_stock = disc['physical_count']
                        item.save()

                        self.log_stock_adjustment(
                            item, old_stock, item.current_stock,
                            f"Physical inventory reconciliation - difference: {disc['difference']:+d}"
                        )

                self.stdout.write(
                    self.style.SUCCESS(f"Created {len(discrepancies)} stock adjustments")
                )

            except Exception as e:
                raise CommandError(f"Failed to create adjustments: {str(e)}")

    def generate_qr_codes(self, options: Dict) -> None:
        """Generate QR codes for inventory items"""
        output_dir = options['output_dir']
        item_codes = options.get('item_codes')
        qr_size = options.get('size', 10)

        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)

        # Build queryset
        if item_codes:
            items = SupplyItem.objects.filter(item_code__in=item_codes)
        else:
            items = SupplyItem.objects.all()

        if not items.exists():
            self.stdout.write(self.style.WARNING("No items found"))
            return

        generated_count = 0

        try:
            for item in items:
                # Create QR code data (could be JSON with item details)
                qr_data = {
                    'item_code': item.item_code,
                    'name': item.name,
                    'category': item.category,
                    'location': item.location
                }

                # Generate QR code
                qr = qrcode.QRCode(
                    version=1,
                    error_correction=qrcode.constants.ERROR_CORRECT_L,
                    box_size=qr_size,
                    border=4,
                )
                qr.add_data(json.dumps(qr_data))
                qr.make(fit=True)

                # Create QR code image
                img = qr.make_image(fill_color="black", back_color="white")

                # Save QR code
                filename = f"{item.item_code}_qr.png"
                filepath = os.path.join(output_dir, filename)
                img.save(filepath)

                generated_count += 1

            self.stdout.write(
                self.style.SUCCESS(f"Generated {generated_count} QR codes in {output_dir}")
            )

        except Exception as e:
            raise CommandError(f"Failed to generate QR codes: {str(e)}")

    def generate_usage_report(self, options: Dict) -> None:
        """Generate usage pattern report"""
        days = options.get('days', 30)
        output_file = options.get('output')
        output_format = options.get('format', 'csv')
        top_items = options.get('top_items', 50)

        # Calculate date range
        end_date = timezone.now()
        start_date = end_date - timedelta(days=days)

        # Get usage data from supply requests
        usage_data = RequestItem.objects.filter(
            request__requested_date__gte=start_date,
            request__status__in=['approved', 'completed']
        ).values(
            'supply_item__item_code',
            'supply_item__name',
            'supply_item__category__name'
        ).annotate(
            total_requested=Sum('quantity_requested'),
            total_approved=Sum('quantity_approved'),
            request_count=Count('request'),
            avg_quantity=Avg('quantity_requested')
        ).order_by('-total_requested')[:top_items]

        # Prepare report data
        report_data = []
        for item in usage_data:
            try:
                supply_item = SupplyItem.objects.get(item_code=item['supply_item__item_code'])

                report_data.append({
                    'item_code': item['supply_item__item_code'],
                    'name': item['supply_item__name'],
                    'category': item['supply_item__category__name'],
                    'current_stock': supply_item.current_stock,
                    'total_requested': item['total_requested'] or 0,
                    'total_approved': item['total_approved'] or 0,
                    'request_count': item['request_count'],
                    'avg_quantity_per_request': float(item['avg_quantity'] or 0),
                    'usage_rate_per_day': float((item['total_approved'] or 0) / days),
                    'days_of_stock_remaining': (
                        supply_item.current_stock / ((item['total_approved'] or 0) / days)
                        if item['total_approved'] else float('inf')
                    ),
                    'unit_price': str(supply_item.unit_cost),
                    'total_cost': str(supply_item.unit_cost * (item['total_approved'] or 0))
                })

            except SupplyItem.DoesNotExist:
                continue

        if not report_data:
            self.stdout.write(self.style.WARNING("No usage data found for the specified period"))
            return

        # Output report
        if output_format == 'csv':
            self.output_csv_report(report_data, output_file, f"Usage Report ({days} days)")
        else:
            self.output_json_report(report_data, output_file, f"Usage Report ({days} days)")

    def update_pricing(self, options: Dict) -> None:
        """Update item pricing from CSV file"""
        file_path = options['file']
        dry_run = options.get('dry_run')

        if not os.path.exists(file_path):
            raise CommandError(f"File '{file_path}' does not exist")

        pricing_updates = []
        errors = []

        try:
            with open(file_path, 'r', newline='', encoding='utf-8') as csvfile:
                reader = csv.DictReader(csvfile)

                if 'item_code' not in reader.fieldnames or 'new_price' not in reader.fieldnames:
                    raise CommandError("CSV must contain 'item_code' and 'new_price' columns")

                for row_num, row in enumerate(reader, start=2):
                    item_code = row['item_code'].strip()

                    try:
                        new_price = Decimal(row['new_price'])
                        if new_price < 0:
                            errors.append(f"Row {row_num}: Price must be non-negative")
                            continue
                    except (ValueError, TypeError):
                        errors.append(f"Row {row_num}: Invalid price '{row['new_price']}'")
                        continue

                    try:
                        item = SupplyItem.objects.get(item_code=item_code)
                        pricing_updates.append({
                            'item': item,
                            'old_price': item.unit_price,
                            'new_price': new_price,
                            'effective_date': row.get('effective_date', timezone.now().date())
                        })
                    except SupplyItem.DoesNotExist:
                        errors.append(f"Row {row_num}: Item '{item_code}' not found")
                        continue

        except Exception as e:
            raise CommandError(f"Failed to process pricing file: {str(e)}")

        if errors:
            self.stdout.write(self.style.ERROR("Validation errors found:"))
            for error in errors:
                self.stdout.write(self.style.ERROR(f"  {error}"))

            if not dry_run:
                raise CommandError("Fix validation errors before updating pricing")

        if dry_run:
            self.stdout.write(
                self.style.SUCCESS(f"Validation successful. {len(pricing_updates)} price updates ready.")
            )
            for update in pricing_updates:
                self.stdout.write(
                    f"  {update['item'].item_code}: {update['old_price']} → {update['new_price']}"
                )
            return

        # Apply pricing updates
        try:
            with transaction.atomic():
                for update in pricing_updates:
                    item = update['item']
                    old_price = item.unit_price
                    item.unit_price = update['new_price']
                    item.save()

                    # Log pricing change
                    logger.info(
                        f"Price update: {item.item_code} ({item.name}) "
                        f"from {old_price} to {item.unit_price}"
                    )

            self.stdout.write(
                self.style.SUCCESS(f"Successfully updated pricing for {len(pricing_updates)} items")
            )

        except Exception as e:
            raise CommandError(f"Failed to update pricing: {str(e)}")

    def generate_inventory_report(self, options: Dict) -> None:
        """Generate comprehensive inventory report"""
        output_file = options.get('output')
        output_format = options.get('format', 'csv')
        include_costs = options.get('include_costs')

        items = SupplyItem.objects.all()

        if not items.exists():
            self.stdout.write(self.style.WARNING("No inventory items found"))
            return

        # Prepare comprehensive report data
        report_data = []
        total_value = Decimal('0')

        for item in items:
            item_value = item.unit_price * item.current_stock
            total_value += item_value

            data = {
                'item_code': item.item_code,
                'name': item.name,
                'description': item.description,
                'category': item.category,
                'unit_of_measure': item.unit_of_measure,
                'current_stock': item.current_stock,
                'reorder_level': item.reorder_level,
                'supplier': item.supplier,
                'location': item.location,
                'status': 'Low Stock' if item.current_stock <= item.reorder_level else 'In Stock'
            }

            if include_costs:
                data.update({
                    'unit_price': str(item.unit_price),
                    'total_value': str(item_value)
                })

            report_data.append(data)

        # Add summary information
        summary = {
            'total_items': len(report_data),
            'low_stock_items': len([item for item in report_data if item['status'] == 'Low Stock']),
            'categories': len(set(item['category'] for item in report_data))
        }

        if include_costs:
            summary['total_inventory_value'] = str(total_value)

        # Output report
        if output_format == 'csv':
            self.output_csv_report(report_data, output_file, "Comprehensive Inventory Report")
        else:
            # For JSON, include summary
            json_data = {
                'summary': summary,
                'items': report_data,
                'generated_at': timezone.now().isoformat()
            }
            self.output_json_data(json_data, output_file, "Comprehensive Inventory Report")

        # Print summary
        self.stdout.write(f"\nInventory Summary:")
        self.stdout.write(f"  Total Items: {summary['total_items']}")
        self.stdout.write(f"  Low Stock Items: {summary['low_stock_items']}")
        self.stdout.write(f"  Categories: {summary['categories']}")
        if include_costs:
            self.stdout.write(f"  Total Value: ${total_value:,.2f}")

    def list_items(self, options: Dict) -> None:
        """List inventory items with filtering"""
        category_filter = options.get('category')
        low_stock_only = options.get('low_stock')
        search_term = options.get('search')
        output_format = options.get('format', 'table')

        # Build queryset
        items = SupplyItem.objects.all()

        if category_filter:
            items = items.filter(category=category_filter)

        if low_stock_only:
            items = items.filter(current_stock__lte=F('reorder_level'))

        if search_term:
            items = items.filter(
                Q(name__icontains=search_term) |
                Q(description__icontains=search_term) |
                Q(item_code__icontains=search_term)
            )

        if not items.exists():
            self.stdout.write(self.style.WARNING("No items found matching criteria"))
            return

        if output_format == 'table':
            self.output_items_table(items)
        else:
            self.output_items_csv(items)

    # Utility methods for output formatting
    def export_to_csv(self, items, output_file: str) -> None:
        """Export items to CSV file"""
        with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = [
                'item_code', 'name', 'description', 'category', 'unit_of_measure',
                'unit_price', 'current_stock', 'reorder_level', 'supplier'
            ]
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()

            for item in items:
                writer.writerow({
                    'item_code': item.item_code,
                    'name': item.name,
                    'description': item.description,
                    'category': item.category.name,
                    'unit_of_measure': item.unit_of_measure,
                    'unit_price': str(item.unit_cost),  # Export as unit_price for CSV compatibility
                    'current_stock': item.current_stock,
                    'reorder_level': item.reorder_level,
                    'supplier': item.supplier
                })

    def export_to_excel(self, items, output_file: str) -> None:
        """Export items to Excel file"""
        try:
            import pandas as pd
        except ImportError:
            raise CommandError("pandas is required for Excel export. Install with: pip install pandas openpyxl")

        data = []
        for item in items:
            data.append({
                'Item Code': item.item_code,
                'Name': item.name,
                'Description': item.description,
                'Category': item.category,
                'Unit of Measure': item.unit_of_measure,
                'Unit Price': float(item.unit_price),
                'Current Stock': item.current_stock,
                'Reorder Level': item.reorder_level,
                'Supplier': item.supplier,
                'Location': item.location
            })

        df = pd.DataFrame(data)
        df.to_excel(output_file, index=False)

    def export_to_json(self, items, output_file: str) -> None:
        """Export items to JSON file"""
        data = []
        for item in items:
            data.append({
                'item_code': item.item_code,
                'name': item.name,
                'description': item.description,
                'category': item.category.name,
                'unit_of_measure': item.unit_of_measure,
                'unit_price': str(item.unit_cost),
                'current_stock': item.current_stock,
                'reorder_level': item.reorder_level,
                'supplier': item.supplier
            })

        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2)

    def output_csv_report(self, data: List[Dict], output_file: Optional[str], title: str) -> None:
        """Output report data in CSV format"""
        import io

        if not data:
            self.stdout.write(self.style.WARNING("No data to report"))
            return

        output = io.StringIO()
        fieldnames = data[0].keys()
        writer = csv.DictWriter(output, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(data)

        content = output.getvalue()

        if output_file:
            with open(output_file, 'w', newline='', encoding='utf-8') as f:
                f.write(content)
            self.stdout.write(
                self.style.SUCCESS(f"{title} saved to {output_file}")
            )
        else:
            self.stdout.write(f"\n{title}:")
            self.stdout.write(content)

    def output_json_report(self, data: List[Dict], output_file: Optional[str], title: str) -> None:
        """Output report data in JSON format"""
        if not data:
            self.stdout.write(self.style.WARNING("No data to report"))
            return

        content = json.dumps(data, indent=2, default=str)

        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(content)
            self.stdout.write(
                self.style.SUCCESS(f"{title} saved to {output_file}")
            )
        else:
            self.stdout.write(f"\n{title}:")
            self.stdout.write(content)

    def output_json_data(self, data: Dict, output_file: Optional[str], title: str) -> None:
        """Output JSON data to file or stdout"""
        content = json.dumps(data, indent=2, default=str)

        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(content)
            self.stdout.write(
                self.style.SUCCESS(f"{title} saved to {output_file}")
            )
        else:
            self.stdout.write(f"\n{title}:")
            self.stdout.write(content)

    def output_items_table(self, items) -> None:
        """Output items in table format"""
        if not items.exists():
            self.stdout.write(self.style.WARNING("No items found"))
            return

        # Table headers
        headers = ['Code', 'Name', 'Category', 'Stock', 'Reorder', 'Price', 'Status']

        # Calculate column widths
        col_widths = [len(header) for header in headers]

        rows = []
        for item in items:
            status = 'LOW' if item.current_stock <= item.reorder_level else 'OK'
            row = [
                item.item_code,
                item.name[:30] + '...' if len(item.name) > 30 else item.name,
                item.category.name,
                str(item.current_stock),
                str(item.reorder_level),
                f"${item.unit_cost:.2f}",
                status
            ]
            rows.append(row)

            # Update column widths
            for i, cell in enumerate(row):
                col_widths[i] = max(col_widths[i], len(str(cell)))

        # Print table
        separator = '+' + '+'.join('-' * (width + 2) for width in col_widths) + '+'

        self.stdout.write(separator)

        # Headers
        header_row = '|' + '|'.join(f" {header:<{col_widths[i]}} " for i, header in enumerate(headers)) + '|'
        self.stdout.write(header_row)
        self.stdout.write(separator)

        # Data rows
        for row in rows:
            data_row = '|' + '|'.join(f" {str(cell):<{col_widths[i]}} " for i, cell in enumerate(row)) + '|'
            self.stdout.write(data_row)

        self.stdout.write(separator)
        self.stdout.write(f"\nTotal items: {len(rows)}")

    def output_items_csv(self, items) -> None:
        """Output items in CSV format"""
        if not items.exists():
            self.stdout.write(self.style.WARNING("No items found"))
            return

        import io

        output = io.StringIO()
        fieldnames = [
            'item_code', 'name', 'category', 'current_stock', 'reorder_level',
            'unit_price', 'supplier', 'status'
        ]
        writer = csv.DictWriter(output, fieldnames=fieldnames)
        writer.writeheader()

        for item in items:
            status = 'LOW' if item.current_stock <= item.reorder_level else 'OK'
            writer.writerow({
                'item_code': item.item_code,
                'name': item.name,
                'category': item.category.name,
                'current_stock': item.current_stock,
                'reorder_level': item.reorder_level,
                'unit_price': str(item.unit_cost),
                'supplier': item.supplier,
                'status': status
            })

        self.stdout.write(output.getvalue())
