{% extends 'base_auth.html' %}

{% block title %}Login{% endblock %}

{% block auth_title %}Sign in to your account{% endblock %}

{% block auth_content %}
<form method="post" class="space-y-6">
    {% csrf_token %}

    {% if form.non_field_errors %}
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
            {% for error in form.non_field_errors %}
                <span class="block sm:inline">{{ error }}</span>
            {% endfor %}
        </div>
    {% endif %}

    <div>
        <label for="{{ form.username.id_for_label }}" class="block text-sm font-medium text-gray-700">Username</label>
        <div class="mt-1">
            <input type="text" name="username" id="{{ form.username.id_for_label }}" 
                   class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm {% if form.username.errors %}border-red-500{% endif %}"
                   required>
        </div>
        {% for error in form.username.errors %}
            <p class="mt-2 text-sm text-red-600">{{ error }}</p>
        {% endfor %}
    </div>

    <div>
        <label for="{{ form.password.id_for_label }}" class="block text-sm font-medium text-gray-700">Password</label>
        <div class="mt-1">
            <input type="password" name="password" id="{{ form.password.id_for_label }}"
                   class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm {% if form.password.errors %}border-red-500{% endif %}"
                   required>
        </div>
        {% for error in form.password.errors %}
            <p class="mt-2 text-sm text-red-600">{{ error }}</p>
        {% endfor %}
    </div>

    <div class="flex items-center justify-between">
        <div class="text-sm">
            <a href="{% url 'suptrack:password_reset' %}" class="font-medium text-blue-600 hover:text-blue-500">
                Forgot your password?
            </a>
        </div>
    </div>

    <div>
        <button type="submit" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
            Sign in
        </button>
    </div>
</form>
<div class="mt-4 text-center">
    <p class="text-sm text-gray-600">
        Don't have an account? 
        <a href="{% url 'suptrack:register' %}" class="font-medium text-blue-600 hover:text-blue-500">
            Sign up
        </a>
    </p>
</div>
{% endblock %}
