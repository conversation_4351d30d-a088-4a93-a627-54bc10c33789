from django import template
from django.db import models
from ..models import SupplyItem

register = template.Library()

@register.simple_tag
def get_low_stock_count():
    """Returns the number of supply items that are at or below their reorder level."""
    # A more efficient way to do this without iterating through all items
    return SupplyItem.objects.filter(current_stock__lte=models.F('reorder_level')).count()
