# Generated by Django 4.2.17 on 2025-07-24 16:05

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name_plural': 'Categories',
                'db_table': 'categories',
            },
        ),
        migrations.CreateModel(
            name='QRCode',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code_id', models.CharField(max_length=50, unique=True)),
                ('qr_image', models.ImageField(blank=True, upload_to='qr_codes/')),
                ('generated_date', models.DateTimeField(auto_now_add=True)),
                ('is_active', models.BooleanField(default=True)),
                ('scanned_count', models.PositiveIntegerField(default=0)),
                ('last_scanned', models.DateTimeField(blank=True, null=True)),
                ('last_scanned_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='scanned_qr_codes', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'qr_codes',
            },
        ),
        migrations.CreateModel(
            name='RequestItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity_requested', models.PositiveIntegerField(validators=[django.core.validators.MinValueValidator(1)])),
                ('quantity_approved', models.PositiveIntegerField(blank=True, null=True, validators=[django.core.validators.MinValueValidator(0)])),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(0)])),
                ('notes', models.TextField(blank=True)),
            ],
            options={
                'db_table': 'request_items',
            },
        ),
        migrations.CreateModel(
            name='UserProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('role', models.CharField(choices=[('admin', 'Administrator'), ('gso_staff', 'GSO Staff'), ('department_user', 'Department User')], max_length=20)),
                ('department', models.CharField(max_length=100)),
                ('phone_number', models.CharField(blank=True, max_length=15)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'user_profiles',
            },
        ),
        migrations.CreateModel(
            name='SupplyRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('request_id', models.CharField(max_length=20, unique=True)),
                ('department', models.CharField(max_length=100)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('released', 'Released'), ('completed', 'Completed')], default='pending', max_length=20)),
                ('justification', models.TextField()),
                ('requested_date', models.DateTimeField(auto_now_add=True)),
                ('required_date', models.DateField()),
                ('approved_date', models.DateTimeField(blank=True, null=True)),
                ('rejection_reason', models.TextField(blank=True)),
                ('notes', models.TextField(blank=True)),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_requests', to=settings.AUTH_USER_MODEL)),
                ('requester', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='supply_requests', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'supply_requests',
            },
        ),
        migrations.CreateModel(
            name='SupplyItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('item_code', models.CharField(max_length=20, unique=True)),
                ('name', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('unit_of_measure', models.CharField(choices=[('piece', 'Piece(s)'), ('box', 'Box(es)'), ('pack', 'Pack(s)'), ('ream', 'Ream(s)'), ('bottle', 'Bottle(s)'), ('roll', 'Roll(s)')], default='piece', max_length=20)),
                ('current_stock', models.PositiveIntegerField(default=0)),
                ('reorder_level', models.PositiveIntegerField(default=10, help_text='Automatically flags item for reorder when quantity falls to this level.')),
                ('minimum_threshold', models.PositiveIntegerField(default=10, validators=[django.core.validators.MinValueValidator(0)])),
                ('unit_cost', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(0)])),
                ('supplier', models.CharField(max_length=200)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('last_updated', models.DateTimeField(auto_now=True)),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='suptrack.category')),
            ],
            options={
                'db_table': 'supply_items',
            },
        ),
        migrations.CreateModel(
            name='StockTransaction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transaction_id', models.CharField(max_length=20, unique=True)),
                ('transaction_type', models.CharField(choices=[('initial_stock', 'Initial Stock'), ('fulfillment', 'Request Fulfillment'), ('adjustment', 'Manual Adjustment'), ('return', 'Stock Return')], max_length=20)),
                ('quantity_change', models.IntegerField()),
                ('new_quantity_on_hand', models.PositiveIntegerField()),
                ('reference_document', models.CharField(blank=True, max_length=100)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('notes', models.TextField(blank=True)),
                ('performed_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('request_item', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='suptrack.requestitem')),
                ('supply_item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transactions', to='suptrack.supplyitem')),
            ],
            options={
                'db_table': 'stock_transactions',
            },
        ),
        migrations.CreateModel(
            name='ScanActivity',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('scan_type', models.CharField(choices=[('general', 'General Scan'), ('issuance', 'Item Issuance'), ('return', 'Item Return'), ('audit', 'Inventory Audit')], default='general', max_length=20)),
                ('scanned_at', models.DateTimeField(auto_now_add=True)),
                ('scan_data', models.TextField(blank=True, default='{}')),
                ('success', models.BooleanField(default=True)),
                ('error_message', models.TextField(blank=True)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user_agent', models.TextField(blank=True)),
                ('qr_code', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='scan_activities', to='suptrack.qrcode')),
                ('scanned_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='scan_activities', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'scan_activities',
            },
        ),
        migrations.AddField(
            model_name='requestitem',
            name='request',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='request_items', to='suptrack.supplyrequest'),
        ),
        migrations.AddField(
            model_name='requestitem',
            name='supply_item',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='suptrack.supplyitem'),
        ),
        migrations.AddField(
            model_name='qrcode',
            name='request_item',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='qr_codes', to='suptrack.requestitem'),
        ),
        migrations.AddField(
            model_name='qrcode',
            name='supply_item',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='qr_codes', to='suptrack.supplyitem'),
        ),
        migrations.AddIndex(
            model_name='supplyrequest',
            index=models.Index(fields=['request_id'], name='supply_requ_request_596db6_idx'),
        ),
        migrations.AddIndex(
            model_name='supplyrequest',
            index=models.Index(fields=['requester', 'status'], name='supply_requ_request_705246_idx'),
        ),
        migrations.AddIndex(
            model_name='supplyrequest',
            index=models.Index(fields=['status', 'requested_date'], name='supply_requ_status_7ee5e4_idx'),
        ),
        migrations.AddIndex(
            model_name='supplyitem',
            index=models.Index(fields=['item_code'], name='supply_item_item_co_34e8f4_idx'),
        ),
        migrations.AddIndex(
            model_name='supplyitem',
            index=models.Index(fields=['category', 'is_active'], name='supply_item_categor_435b89_idx'),
        ),
        migrations.AddIndex(
            model_name='supplyitem',
            index=models.Index(fields=['current_stock'], name='supply_item_current_6e80c9_idx'),
        ),
        migrations.AddIndex(
            model_name='stocktransaction',
            index=models.Index(fields=['transaction_id'], name='stock_trans_transac_4fdecc_idx'),
        ),
        migrations.AddIndex(
            model_name='stocktransaction',
            index=models.Index(fields=['supply_item', 'timestamp'], name='stock_trans_supply__b287e2_idx'),
        ),
        migrations.AddIndex(
            model_name='stocktransaction',
            index=models.Index(fields=['transaction_type', 'timestamp'], name='stock_trans_transac_b3e9ac_idx'),
        ),
        migrations.AddIndex(
            model_name='scanactivity',
            index=models.Index(fields=['qr_code', 'scanned_at'], name='scan_activi_qr_code_2ab60f_idx'),
        ),
        migrations.AddIndex(
            model_name='scanactivity',
            index=models.Index(fields=['scanned_by', 'scanned_at'], name='scan_activi_scanned_940a51_idx'),
        ),
        migrations.AddIndex(
            model_name='scanactivity',
            index=models.Index(fields=['scan_type', 'scanned_at'], name='scan_activi_scan_ty_efab92_idx'),
        ),
        migrations.AddIndex(
            model_name='requestitem',
            index=models.Index(fields=['request', 'supply_item'], name='request_ite_request_8ba111_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='requestitem',
            unique_together={('request', 'supply_item')},
        ),
        migrations.AddIndex(
            model_name='qrcode',
            index=models.Index(fields=['code_id'], name='qr_codes_code_id_823abe_idx'),
        ),
        migrations.AddIndex(
            model_name='qrcode',
            index=models.Index(fields=['supply_item', 'is_active'], name='qr_codes_supply__69b7d0_idx'),
        ),
        migrations.AddIndex(
            model_name='qrcode',
            index=models.Index(fields=['request_item'], name='qr_codes_request_a03e0a_idx'),
        ),
    ]
