from django.contrib.auth.decorators import user_passes_test
from django.shortcuts import redirect
from django.contrib import messages

def gso_staff_required(function=None, redirect_field_name=None, login_url='suptrack:login'):
    """
    Decorator for views that checks that the user is a member of the GSO Staff group.
    """
    actual_decorator = user_passes_test(
        lambda u: u.is_active and u.groups.filter(name='GSO Staff').exists(),
        login_url=login_url
    )

    def decorator(view_func):
        def _wrapped_view(request, *args, **kwargs):
            if not request.user.groups.filter(name='GSO Staff').exists():
                messages.error(request, 'You do not have permission to access this page.')
                return redirect('suptrack:dashboard')  # Redirect to their own dashboard
            return view_func(request, *args, **kwargs)
        return _wrapped_view

    if function:
        return actual_decorator(function)
    return actual_decorator

# A simpler version that can be directly applied
def gso_required(view_func):
    def _wrapped_view(request, *args, **kwargs):
        if not request.user.groups.filter(name='GSO Staff').exists():
            messages.error(request, 'You do not have permission to access this page.')
            return redirect('suptrack:dashboard')
        return view_func(request, *args, **kwargs)
    return _wrapped_view
