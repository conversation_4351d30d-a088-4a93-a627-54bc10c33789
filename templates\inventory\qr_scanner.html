{% extends 'base.html' %}

{% block content %}
<div class="max-w-4xl mx-auto py-10 px-4 sm:px-6 lg:px-8">
    <h1 class="text-3xl font-bold text-gray-900 mb-6">QR Code Scanner</h1>

    <div class="mb-4">
        <label for="scan-mode" class="block text-sm font-medium text-gray-700">Scan Mode</label>
        <select id="scan-mode" name="scan-mode" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
            <option value="general">General <PERSON>an</option>
            <option value="audit">Inventory Audit</option>
        </select>
    </div>

    <div id="qr-reader" style="width: 500px"></div>
    <div id="qr-reader-results"></div>

</div>

<script src="https://unpkg.com/html5-qrcode"></script>
<script>
    function onScanSuccess(decodedText, decodedResult) {
        const scanMode = document.getElementById('scan-mode').value;
        let url;

        if (scanMode === 'audit') {
            url = `/inventory/audit-scan/${decodedText}/`;
        } else {
            url = `/inventory/process-scan/${decodedText}/`;
        }

        window.location.href = url;
    }

    function onScanFailure(error) {
        // handle scan failure, usually better to ignore and keep scanning.
        // console.warn(`Code scan error = ${error}`);
    }

    let html5QrcodeScanner = new Html5QrcodeScanner(
        "qr-reader",
        {
            fps: 10,
            qrbox: { width: 250, height: 250 }
        },
        /* verbose= */ false);
    html5QrcodeScanner.render(onScanSuccess, onScanFailure);
</script>
{% endblock %}
