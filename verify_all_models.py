import os
import sys
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smartsupply.settings')
django.setup()

from django.contrib.auth.models import User
from django.utils import timezone
from datetime import timedelta
import uuid
from suptrack.models import (
    UserProfile, Category, SupplyItem, SupplyRequest, 
    RequestItem, StockTransaction, QRCode, ScanActivity
)

def verify_all_models():
    print("\n=== Verifying All Models ===\n")
    
    # Check if all models exist
    print("Checking model existence...")
    models = [
        UserProfile, Category, SupplyItem, SupplyRequest,
        RequestItem, StockTransaction, QRCode, ScanActivity
    ]
    
    for model in models:
        print(f"{model.__name__} model exists")
    
    # Check model counts
    print("\nChecking model counts...")
    print(f"UserProfile count: {UserProfile.objects.count()}")
    print(f"Category count: {Category.objects.count()}")
    print(f"SupplyItem count: {SupplyItem.objects.count()}")
    print(f"SupplyRequest count: {SupplyRequest.objects.count()}")
    print(f"RequestItem count: {RequestItem.objects.count()}")
    print(f"StockTransaction count: {StockTransaction.objects.count()}")
    print(f"QRCode count: {QRCode.objects.count()}")
    print(f"ScanActivity count: {ScanActivity.objects.count()}")
    
    # Check model relationships
    print("\nChecking model relationships...")
    
    # UserProfile -> User
    if UserProfile.objects.exists():
        sample_profile = UserProfile.objects.first()
        print(f"UserProfile -> User: {sample_profile.user.username}")
    
    # SupplyItem -> Category
    if SupplyItem.objects.exists():
        sample_item = SupplyItem.objects.first()
        print(f"SupplyItem -> Category: {sample_item.category.name}")
    
    # SupplyRequest -> User
    if SupplyRequest.objects.exists():
        sample_request = SupplyRequest.objects.first()
        print(f"SupplyRequest -> User: {sample_request.requester.username}")
    
    # RequestItem -> SupplyRequest, SupplyItem
    if RequestItem.objects.exists():
        sample_request_item = RequestItem.objects.first()
        print(f"RequestItem -> SupplyRequest: {sample_request_item.request.request_id}")
        print(f"RequestItem -> SupplyItem: {sample_request_item.supply_item.name}")
    
    # StockTransaction -> SupplyItem, User
    if StockTransaction.objects.exists():
        sample_transaction = StockTransaction.objects.first()
        print(f"StockTransaction -> SupplyItem: {sample_transaction.supply_item.name}")
        print(f"StockTransaction -> User: {sample_transaction.performed_by.username}")
    
    # QRCode -> SupplyItem, RequestItem
    if QRCode.objects.exists():
        sample_qrcode = QRCode.objects.first()
        print(f"QRCode -> SupplyItem: {sample_qrcode.supply_item.name}")
        print(f"QRCode -> RequestItem: {sample_qrcode.request_item.id}")
    
    # ScanActivity -> QRCode, User
    if ScanActivity.objects.exists():
        sample_activity = ScanActivity.objects.first()
        print(f"ScanActivity -> QRCode: {sample_activity.qr_code.code_id if sample_activity.qr_code else 'None'}")
        print(f"ScanActivity -> User: {sample_activity.scanned_by.username}")
    
    # Check model properties
    print("\nChecking model properties...")
    
    # SupplyItem properties
    if SupplyItem.objects.exists():
        sample_item = SupplyItem.objects.first()
        print(f"SupplyItem.is_low_stock: {sample_item.is_low_stock}")
        print(f"SupplyItem.total_value: {sample_item.total_value}")
    
    # SupplyRequest properties
    if SupplyRequest.objects.exists():
        sample_request = SupplyRequest.objects.first()
        print(f"SupplyRequest.total_items: {sample_request.total_items}")
        print(f"SupplyRequest.total_cost: {sample_request.total_cost}")
    
    # RequestItem properties
    if RequestItem.objects.exists():
        sample_request_item = RequestItem.objects.first()
        print(f"RequestItem.total_cost: {sample_request_item.total_cost}")
        print(f"RequestItem.approved_total_cost: {sample_request_item.approved_total_cost}")
    
    print("\nVerification complete! All models are working correctly.")

if __name__ == '__main__':
    verify_all_models()