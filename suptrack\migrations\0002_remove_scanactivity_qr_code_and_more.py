# Generated by Django 4.2.17 on 2025-07-24 16:17

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('suptrack', '0001_initial'),
    ]

    operations = [
        migrations.DeleteModel(
            name='ScanActivity',
        ),
        migrations.DeleteModel(
            name='QRCode',
        ),
        migrations.RemoveField(
            model_name='supplyitem',
            name='minimum_threshold',
        ),
        migrations.AlterField(
            model_name='supplyitem',
            name='reorder_level',
            field=models.PositiveIntegerField(default=10, help_text='When stock falls to this level, the item is flagged for reorder.'),
        ),
    ]
