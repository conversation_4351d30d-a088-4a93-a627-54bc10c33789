{% extends 'base.html' %}

{% block title %}Low Stock Alerts{% endblock %}

{% block content %}
<div class="bg-white p-8 rounded-lg shadow-lg">
    <h1 class="text-3xl font-bold text-gray-800 mb-4">Low Stock Items</h1>
    <p class="text-gray-600 mb-6">The following items have fallen below their reorder level and may need to be restocked soon.</p>

    <div class="overflow-x-auto bg-white rounded-lg shadow">
        <table class="min-w-full leading-normal">
            <thead>
                <tr>
                    <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                        Item Name
                    </th>
                    <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                        Category
                    </th>
                    <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider">
                        Current Stock
                    </th>
                    <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider">
                        Reorder Level
                    </th>
                    <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">
                        Actions
                    </th>
                </tr>
            </thead>
            <tbody>
                {% for item in low_stock_items %}
                <tr>
                    <td class="px-5 py-4 border-b border-gray-200 bg-white text-sm">
                        <a href="{% url 'suptrack:item_detail' item.pk %}" class="text-blue-600 hover:underline">{{ item.name }}</a>
                    </td>
                    <td class="px-5 py-4 border-b border-gray-200 bg-white text-sm">
                        <p class="text-gray-900 whitespace-no-wrap">{{ item.category.name }}</p>
                    </td>
                    <td class="px-5 py-4 border-b border-gray-200 bg-white text-sm text-right">
                        <p class="text-red-600 font-semibold whitespace-no-wrap">{{ item.current_stock }}</p>
                    </td>
                    <td class="px-5 py-4 border-b border-gray-200 bg-white text-sm text-right">
                        <p class="text-gray-900 whitespace-no-wrap">{{ item.reorder_level }}</p>
                    </td>
                    <td class="px-5 py-4 border-b border-gray-200 bg-white text-sm text-center">
                        <a href="{% url 'suptrack:edit_item' item.pk %}" class="text-indigo-600 hover:text-indigo-900">Restock</a>
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="5" class="text-center py-10 px-5 text-gray-500">
                        <p class="text-lg font-semibold">All items are well-stocked!</p>
                        <p>There are currently no items below their reorder level.</p>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endblock %}
