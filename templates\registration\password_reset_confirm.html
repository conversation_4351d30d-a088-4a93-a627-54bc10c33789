{% extends 'base_auth.html' %}

{% block title %}Enter new password{% endblock %}

{% block auth_title %}Set a new password{% endblock %}

{% block auth_content %}
{% if validlink %}
    <form method="post" class="space-y-6">
        {% csrf_token %}
        {% if form.non_field_errors %}
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                {% for error in form.non_field_errors %}
                    <span class="block sm:inline">{{ error }}</span>
                {% endfor %}
            </div>
        {% endif %}

        {% for field in form %}
            <div>
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ field.label }}</label>
                <div class="mt-1">
                    <input type="password" name="{{ field.name }}" id="{{ field.id_for_label }}" 
                           class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm {% if field.errors %}border-red-500{% endif %}"
                           required>
                </div>
                {% for error in field.errors %}
                    <p class="mt-2 text-sm text-red-600">{{ error }}</p>
                {% endfor %}
            </div>
        {% endfor %}

        <div>
            <button type="submit" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                Change my password
            </button>
        </div>
    </form>
{% else %}
    <div class="text-center">
        <p class="text-red-600">
            The password reset link was invalid, possibly because it has already been used. Please request a new password reset.
        </p>
        <div class="mt-6">
            <a href="{% url 'suptrack:password_reset' %}" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                Request a new password reset
            </a>
        </div>
    </div>
{% endif %}
{% endblock %}
