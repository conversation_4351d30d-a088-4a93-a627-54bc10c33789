{% extends 'base.html' %}

{% block title %}Inventory Dashboard{% endblock %}

{% block content %}
<div class="bg-white p-8 rounded-lg shadow-lg">
    <h1 class="text-3xl font-bold text-gray-800 mb-4">Inventory Management</h1>

    <div class="mb-6 bg-white p-4 rounded-lg shadow-md">
        <form hx-get="{% url 'suptrack:inventory_dashboard' %}" hx-target="#inventory-list" hx-swap="innerHTML" hx-trigger="submit, keyup delay:300ms from:[name='q']">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div class="md:col-span-2">
                    <input type="text" name="q" value="{{ search_query|default:'' }}" class="w-full px-4 py-2 border border-gray-300 rounded-lg" placeholder="Search by item name...">
                </div>
                <div>
                    <select name="category" class="w-full px-4 py-2 border border-gray-300 rounded-lg">
                        <option value="">All Categories</option>
                        {% for category in categories %}
                            <option value="{{ category.id }}" {% if category.id|stringformat:"s" == selected_category %}selected{% endif %}>{{ category.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div>
                    <select name="stock_status" class="w-full px-4 py-2 border border-gray-300 rounded-lg">
                        <option value="">All Stock Statuses</option>
                        <option value="in_stock" {% if selected_stock_status == 'in_stock' %}selected{% endif %}>In Stock</option>
                        <option value="low_stock" {% if selected_stock_status == 'low_stock' %}selected{% endif %}>Low Stock</option>
                    </select>
                </div>
            </div>
        </form>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="md:col-span-2">
            <div class="bg-white p-6 rounded-lg shadow-lg">
                <h2 class="text-2xl font-bold text-gray-800 mb-4">Current Inventory</h2>
                <div id="inventory-list">
                    {% include 'inventory/partials/inventory_list.html' %}
                </div>
            </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow-lg">
            <h2 class="text-2xl font-bold text-gray-800 mb-4">Add New Item</h2>
            <form hx-post="{% url 'suptrack:inventory_dashboard' %}" hx-target="#inventory-list" hx-swap="innerHTML" hx-on::after-request="this.reset(); htmx.trigger('#inventory-list', 'load');">
                {% csrf_token %}
                <div class="space-y-4">
                    {{ form.as_p }}
                </div>
                <button type="submit" class="mt-6 w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    Add Item
                </button>
            </form>
        </div>
    </div>
</div>
{% endblock %}
